# 月报告系统Docker部署快速指南

## 🚀 快速开始

### 在Windows机器上（联网）

1. **构建和打包**
```cmd
build_docker.bat
```

2. **传输到服务器**
```bash
# 压缩并上传docker_package目录到Ubuntu服务器
```

### 在Ubuntu服务器上（离线）

1. **一键部署**
```bash
cd docker_package
chmod +x deploy_docker.sh
./deploy_docker.sh
```

2. **选择模式**
- 选择 `3` - 完整模式（推荐）
- 系统会自动：
  - 生成2025年1-6月历史报告
  - 设置每月3号定时任务
  - 启动Web服务

## 📋 三种部署模式详解

### 1. 批量模式（一次性）
**用途**: 生成2025年1-6月历史报告
```bash
sudo docker-compose --profile batch up
```

### 2. 定时任务模式（持续运行）
**用途**: 每月3号凌晨2点自动生成上个月报告
```bash
sudo docker-compose --profile cron up -d
```

### 3. 完整模式（推荐）
**用途**: 包含Web服务 + 定时任务 + 初始批量生成
```bash
sudo docker-compose --profile daemon up -d
```

## 🔧 常用管理命令

### 查看服务状态
```bash
sudo docker-compose ps
```

### 查看日志
```bash
# 查看所有日志
sudo docker-compose logs -f

# 查看定时任务日志
sudo docker-compose --profile cron logs -f

# 查看完整服务日志
sudo docker-compose --profile daemon logs -f
```

### 停止服务
```bash
# 停止定时任务
sudo docker-compose --profile cron down

# 停止完整服务
sudo docker-compose --profile daemon down
```

### 重启服务
```bash
sudo docker-compose restart
```

## 📁 输出文件位置

- **报告文件**: `./output/reports/`
- **图表文件**: `./output/charts/`
- **日志文件**: `./output/logs/`

## ⏰ 定时任务说明

- **执行时间**: 每月3号凌晨2点
- **生成内容**: 上个月的月报告
- **自动运行**: 无需人工干预

例如：
- 8月3日 → 生成7月报告
- 9月3日 → 生成8月报告
- 1月3日 → 生成12月报告

## 🛠️ 手动执行报告生成

### 进入容器
```bash
sudo docker exec -it monthly-reports-cron bash
```

### 生成上个月报告
```bash
python generate_monthly_reports.py monthly
```

### 生成指定月份报告
```bash
# 生成2025年7月报告
python generate_monthly_reports.py custom 2025 7

# 生成多个月份报告
python generate_monthly_reports.py custom 2025 7,8,9
```

### 重新生成历史报告
```bash
python generate_monthly_reports.py batch
```

## 🔍 故障排除

### 1. 检查容器状态
```bash
sudo docker ps -a
```

### 2. 查看详细错误
```bash
sudo docker-compose logs --tail=100
```

### 3. 检查定时任务
```bash
sudo docker exec -it monthly-reports-cron crontab -l
```

### 4. 测试数据库连接
```bash
sudo docker exec -it monthly-reports-cron python -c "from database.connection import db_manager; print(db_manager.test_connection())"
```

### 5. 重新加载镜像
```bash
sudo docker load -i monthly-reports_latest.tar
```

## 📊 监控和维护

### 查看磁盘使用
```bash
du -sh output/
```

### 清理旧文件
```bash
# 清理30天前的日志
find output/logs/ -name "*.log" -mtime +30 -delete

# 清理旧报告（可选）
find output/reports/ -name "*.docx" -mtime +90 -delete
```

### 备份重要数据
```bash
tar -czf backup_$(date +%Y%m%d).tar.gz output/
```

## 🎯 推荐部署流程

1. **首次部署**：选择完整模式
2. **验证运行**：检查批量生成是否成功
3. **确认定时任务**：查看crontab设置
4. **监控日志**：观察系统运行状态
5. **定期维护**：清理日志和备份数据

## 📞 技术支持

如果遇到问题，请提供以下信息：
1. 错误日志：`sudo docker-compose logs`
2. 容器状态：`sudo docker ps -a`
3. 系统信息：`uname -a`
4. Docker版本：`docker --version`
