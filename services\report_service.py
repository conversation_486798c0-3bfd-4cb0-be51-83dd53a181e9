"""
Word报告生成服务
"""
from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn
import os
from datetime import datetime
from typing import Dict, Any, List
from loguru import logger
from config import settings


class ReportService:
    """报告生成服务"""
    
    def __init__(self):
        self.output_dir = settings.report_output_dir
    
    def generate_comprehensive_report(self, analysis_data: Dict[str, Any],
                                     chart_paths: List[str],
                                     llm_content: Dict[str, Any],
                                     special_analysis: Dict[str, Any],
                                     overview_stats: Dict[str, Any],
                                     time_range: str,
                                     fkdwdm: str = None) -> str:
        """生成全面深度分析的Word报告"""
        try:
            # 创建文档
            doc = Document()

            # 设置文档样式
            self._setup_document_style(doc)

            # 添加标题页
            self._add_comprehensive_title_page(doc, time_range, fkdwdm)



            # 1. 总警情概况（包含同比环比）
            self._add_comprehensive_police_overview(doc, overview_stats, chart_paths)

            # 2. 刑事案件与治安案件分析
            self._add_comprehensive_criminal_security_analysis(doc, special_analysis, llm_content, chart_paths)

            # 3. 盗窃案件分析
            self._add_comprehensive_theft_analysis(doc, special_analysis, llm_content, chart_paths)

            # 4. 涉黄与涉赌警情分析
            self._add_comprehensive_vice_analysis(doc, special_analysis, llm_content, chart_paths)

            # 5. 打架斗殴警情分析
            self._add_comprehensive_fight_analysis(doc, special_analysis, llm_content, chart_paths)

            # 6. 综合防范建议
            self._add_comprehensive_recommendations(doc, llm_content)

            # 保存文档 - 使用新的命名规则
            filename = self._generate_report_filename(time_range, fkdwdm)
            filepath = os.path.join(self.output_dir, filename)
            doc.save(filepath)

            logger.info(f"新结构Word报告生成成功: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"新结构Word报告生成失败: {e}")
            raise

    def generate_report(self, analysis_data: Dict[str, Any],
                       chart_paths: List[str],
                       llm_content: Dict[str, Any],
                       time_range: str,
                       fkdwdm: str = None) -> str:
        """生成完整的Word报告"""
        try:
            # 创建文档
            doc = Document()

            # 设置文档样式
            self._setup_document_style(doc)

            # 添加标题页
            self._add_title_page(doc, time_range)



            # 添加执行摘要
            self._add_executive_summary(doc, llm_content.get('summary', ''))

            # 添加详细分析（包含同比环比）
            self._add_detailed_analysis(doc, analysis_data, chart_paths, analysis_data.get('comparison_analysis', {}))

            # 添加综合分析（融合LLM分析和同比环比分析）
            self._add_comprehensive_analysis(doc, llm_content.get('analysis', ''),
                                           llm_content.get('comparison_analysis', ''),
                                           analysis_data.get('comparison_analysis', {}))

            # 添加关键洞察
            self._add_key_insights(doc, llm_content.get('insights', []))

            # 添加结论和建议
            self._add_conclusions(doc, analysis_data)

            # 保存文档 - 使用新的命名规则
            filename = self._generate_report_filename(time_range, fkdwdm)
            filepath = os.path.join(self.output_dir, filename)
            doc.save(filepath)

            logger.info(f"Word报告生成成功: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Word报告生成失败: {e}")
            raise
    
    def _setup_document_style(self, doc: Document):
        """设置文档样式"""
        # 设置默认字体
        style = doc.styles['Normal']
        font = style.font
        font.name = '宋体'
        font.size = Pt(12)
        
        # 设置标题样式
        heading1 = doc.styles['Heading 1']
        heading1.font.name = '黑体'
        heading1.font.size = Pt(16)
        heading1.font.bold = True
        
        heading2 = doc.styles['Heading 2']
        heading2.font.name = '黑体'
        heading2.font.size = Pt(14)
        heading2.font.bold = True
    
    def _add_title_page(self, doc: Document, time_range: str):
        """添加标题页"""
        # 主标题
        title = doc.add_heading('警情数据分析报告', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 副标题
        subtitle = doc.add_paragraph(f'数据时间范围：{time_range}')
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
        subtitle_format = subtitle.runs[0].font
        subtitle_format.size = Pt(14)
        subtitle_format.color.rgb = RGBColor(128, 128, 128)
        
        # 空行
        doc.add_paragraph()
        doc.add_paragraph()
        
        # 生成信息
        info_para = doc.add_paragraph()
        info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        info_text = f"""
生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M')}
生成系统：警情数据分析报告生成系统
版本：1.0.0
        """
        
        info_run = info_para.add_run(info_text.strip())
        info_run.font.size = Pt(12)
        info_run.font.color.rgb = RGBColor(100, 100, 100)
        
        # 分页
        doc.add_page_break()
    

    
    def _add_executive_summary(self, doc: Document, summary_text: str):
        """添加执行摘要"""
        doc.add_heading('执行摘要', level=1)
        
        if summary_text:
            doc.add_paragraph(summary_text)
        else:
            doc.add_paragraph('本报告基于警情数据库中的真实数据，通过多维度分析，为公安机关提供数据支撑和决策参考。')
        
        doc.add_paragraph()
    

    
    def _add_detailed_analysis(self, doc: Document, analysis_data: Dict[str, Any],
                             chart_paths: List[str], comparison_data: Dict[str, Any] = None):
        """添加详细分析（包含同比环比）"""
        # 时间分布分析
        self._add_time_analysis(doc, analysis_data.get('time_analysis', {}), chart_paths, comparison_data)

        # 地域分布分析
        self._add_geographic_analysis(doc, analysis_data.get('geographic_analysis', {}), chart_paths, comparison_data)

        # 警情类别分析
        self._add_category_analysis(doc, analysis_data.get('category_analysis', {}), chart_paths, comparison_data)
    
    def _add_time_analysis(self, doc: Document, time_data: Dict[str, Any], chart_paths: List[str], comparison_data: Dict[str, Any] = None):
        """添加时间分布分析"""
        doc.add_heading('时间分布分析', level=1)

        # 文字描述
        peak_hours = time_data.get('peak_hours', [])
        if peak_hours:
            doc.add_paragraph(f"数据显示，警情高峰时段主要集中在{', '.join(peak_hours[:3])}。")

        # 添加同比环比分析（如果有数据）
        if comparison_data:
            self._add_time_comparison_text(doc, comparison_data)

        # 插入相关图表
        for chart_path in chart_paths:
            chart_filename = os.path.basename(chart_path).lower()
            if chart_filename == 'hourly_distribution.png':
                self._add_image_to_doc(doc, chart_path)
                break

        doc.add_paragraph()
    
    def _add_geographic_analysis(self, doc: Document, geo_data: Dict[str, Any], chart_paths: List[str], comparison_data: Dict[str, Any] = None):
        """添加地域分布分析"""
        doc.add_heading('地域分布分析', level=1)

        # 区县排名
        county_ranking = geo_data.get('county_ranking', [])
        if county_ranking:
            doc.add_paragraph(f"警情数量排名前五的区县为：{', '.join(county_ranking[:5])}。")

        # 添加同比环比分析（如果有数据）
        if comparison_data:
            self._add_geographic_comparison_text(doc, comparison_data)

        # 插入相关图表
        for chart_path in chart_paths:
            chart_filename = os.path.basename(chart_path).lower()
            if chart_filename in ['county_ranking.png', 'geographic_distribution.png', 'community_distribution.png']:
                self._add_image_to_doc(doc, chart_path)
                break

        doc.add_paragraph()
    
    def _add_category_analysis(self, doc: Document, category_data: Dict[str, Any], chart_paths: List[str], comparison_data: Dict[str, Any] = None):
        """添加警情类别分析"""
        doc.add_heading('警情类别分析', level=1)

        # 主要类别
        category_dist = category_data.get('category_distribution', {})
        if category_dist:
            top_categories = list(category_dist.keys())[:3]
            doc.add_paragraph(f"主要警情类别包括：{', '.join(top_categories)}。")

        # 添加同比环比分析（如果有数据）
        if comparison_data:
            self._add_category_comparison_text(doc, comparison_data)

        # 插入相关图表
        for chart_path in chart_paths:
            chart_filename = os.path.basename(chart_path).lower()
            if chart_filename == 'category_distribution.png':
                self._add_image_to_doc(doc, chart_path)
                break

        doc.add_paragraph()

    def _add_time_comparison_text(self, doc: Document, comparison_data: Dict[str, Any]):
        """添加时间分析的同比环比文本"""
        if not comparison_data:
            return

        # 查找时间相关的变化数据
        changes = comparison_data.get('changes', {})
        if changes:
            total_change = changes.get('total_count', {})
            if total_change:
                change_pct = total_change.get('change_percentage', 0)
                if change_pct > 0:
                    doc.add_paragraph(f"与对比期间相比，警情总量增长了{change_pct:.1f}%。")
                elif change_pct < 0:
                    doc.add_paragraph(f"与对比期间相比，警情总量下降了{abs(change_pct):.1f}%。")

    def _add_geographic_comparison_text(self, doc: Document, comparison_data: Dict[str, Any]):
        """添加地域分析的同比环比文本"""
        if not comparison_data:
            return

        # 可以添加区县变化的具体描述
        doc.add_paragraph("地域分布呈现一定的变化趋势，详见对比分析图表。")

    def _add_category_comparison_text(self, doc: Document, comparison_data: Dict[str, Any]):
        """添加类别分析的同比环比文本"""
        if not comparison_data:
            return

        # 可以添加类别变化的具体描述
        doc.add_paragraph("警情类别分布出现变化，具体变化情况详见对比分析图表。")





    def _add_comprehensive_analysis(self, doc: Document, analysis_text: str,
                                   comparison_text: str = '', comparison_data: Dict[str, Any] = None):
        """添加综合分析（融合LLM分析和同比环比分析）"""
        doc.add_heading('综合分析', level=1)

        # 添加基础分析文本
        if analysis_text:
            # 分段处理
            paragraphs = analysis_text.split('\n\n')
            for para in paragraphs:
                if para.strip():
                    doc.add_paragraph(para.strip())
        else:
            doc.add_paragraph('基于数据分析结果，警情分布呈现一定的规律性，为公安工作提供了重要参考。')

        # 融入同比环比分析
        if comparison_data or comparison_text:
            doc.add_paragraph()  # 添加空行分隔

            # 添加同比环比数据分析
            if comparison_data:
                self._add_integrated_comparison_analysis(doc, comparison_data)

            # 添加同比环比LLM分析文本
            if comparison_text:
                paragraphs = comparison_text.split('\n\n')
                for para in paragraphs:
                    if para.strip():
                        doc.add_paragraph(para.strip())

        doc.add_paragraph()

    def _add_integrated_comparison_analysis(self, doc: Document, comparison_data: Dict[str, Any]):
        """添加融合的同比环比分析"""
        # 环比分析
        period_comparison = comparison_data.get('period_comparison')
        if period_comparison:
            changes = period_comparison.get('changes', {})
            if changes:
                # 构建环比分析文本
                comparison_points = []

                total_change = changes.get('total_count', {})
                if total_change:
                    change_pct = total_change.get('change_percentage', 0)
                    if abs(change_pct) > 1:  # 变化超过1%才提及
                        if change_pct > 0:
                            comparison_points.append(f"警情总量环比增长{change_pct:.1f}%")
                        else:
                            comparison_points.append(f"警情总量环比下降{abs(change_pct):.1f}%")

                # 移除出警率相关分析，基于cjqk字段

                if comparison_points:
                    comparison_text = "从环比数据来看，" + "，".join(comparison_points) + "。"
                    doc.add_paragraph(comparison_text)

        # 同比分析
        year_comparison = comparison_data.get('year_comparison')
        if year_comparison:
            changes = year_comparison.get('changes', {})
            if changes:
                # 构建同比分析文本
                comparison_points = []

                total_change = changes.get('total_count', {})
                if total_change:
                    change_pct = total_change.get('change_percentage', 0)
                    if abs(change_pct) > 1:
                        if change_pct > 0:
                            comparison_points.append(f"警情总量同比增长{change_pct:.1f}%")
                        else:
                            comparison_points.append(f"警情总量同比下降{abs(change_pct):.1f}%")

                if comparison_points:
                    comparison_text = "从同比数据来看，" + "，".join(comparison_points) + "。"
                    doc.add_paragraph(comparison_text)
    
    def _add_key_insights(self, doc: Document, insights: List[str]):
        """添加关键洞察"""
        doc.add_heading('关键洞察', level=1)
        
        if insights:
            for insight in insights:
                para = doc.add_paragraph()
                para.style = 'List Bullet'
                para.add_run(insight)
        else:
            doc.add_paragraph('• 数据分析揭示了警情分布的重要规律')
            doc.add_paragraph('• 为警力配置和勤务安排提供了科学依据')
        
        doc.add_paragraph()
    
    def _add_conclusions(self, doc: Document, analysis_data: Dict[str, Any]):
        """添加结论和建议"""
        doc.add_heading('结论与建议', level=1)

        doc.add_paragraph('基于本次数据分析，提出以下建议：')

        suggestions = [
            '根据时间分布规律，合理安排警力勤务时间',
            '针对高发区域加强巡逻防控力度',
            '建立数据驱动的预警机制',
            '持续监控数据变化趋势，及时调整策略'
        ]

        # 手动添加编号，避免与前面的编号列表冲突
        for i, suggestion in enumerate(suggestions, 1):
            para = doc.add_paragraph(f'{i}. {suggestion}')
            # 不使用List Number样式，直接手动编号
    
    def _add_image_to_doc(self, doc: Document, image_path: str):
        """向文档添加图片"""
        try:
            if os.path.exists(image_path):
                para = doc.add_paragraph()
                para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                run = para.runs[0] if para.runs else para.add_run()
                run.add_picture(image_path, width=Inches(6))

                # 添加图片说明 - 使用中文名称
                caption = doc.add_paragraph()
                caption.alignment = WD_ALIGN_PARAGRAPH.CENTER

                # 获取中文图表名称
                english_filename = os.path.basename(image_path)
                chinese_name = self._get_chart_chinese_name(english_filename)

                caption_run = caption.add_run(f"图：{chinese_name}")
                caption_run.font.size = Pt(10)
                caption_run.font.color.rgb = RGBColor(128, 128, 128)

        except Exception as e:
            logger.warning(f"图片插入失败: {image_path} - {e}")

    def _get_chart_chinese_name(self, english_filename: str) -> str:
        """
        根据英文文件名获取中文显示名

        Args:
            english_filename: 英文文件名（包含.png扩展名）

        Returns:
            中文显示名
        """
        # 导入图表服务来获取映射
        from services.chart_service import enhanced_chart_service
        return enhanced_chart_service.get_chart_chinese_name(english_filename)

    def _add_new_title_page(self, doc: Document, time_range: str, fkdwdm: str = None):
        """添加新的标题页"""
        # 主标题
        title = doc.add_heading('警情数据深度分析报告', level=0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 副标题
        subtitle_text = f"数据时间范围：{time_range}"
        if fkdwdm:
            # 获取派出所名称
            from services.data_service import data_service
            station_name = data_service.get_police_station_name(fkdwdm)
            # 只显示派出所名称，不显示"派出所："前缀
            subtitle_text += f"\n{station_name}"

        subtitle = doc.add_paragraph(subtitle_text)
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 生成时间
        doc.add_paragraph()
        time_para = doc.add_paragraph(f"报告生成时间：{datetime.now().strftime('%Y年%m月%d日')}")
        time_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        doc.add_page_break()

    def _add_new_table_of_contents(self, doc: Document):
        """添加新的目录"""
        doc.add_heading('目录', level=1)

        contents = [
            "一、总警情概况",
            "二、刑事案件与治安案件深度分析",
            "三、盗窃案件深度分析",
            "四、涉黄与涉赌警情深度分析",
            "五、打架斗殴警情深度分析",
            "六、图表展示"
        ]

        for content in contents:
            doc.add_paragraph(content)

        doc.add_page_break()

    def _add_police_overview(self, doc: Document, overview_stats: Dict[str, int],
                           comparison_data: Dict[str, Any]):
        """添加总警情概况"""
        doc.add_heading('一、总警情概况', level=1)

        # 基础统计
        doc.add_paragraph('本期警情统计数据如下：')

        for police_type, count in overview_stats.items():
            doc.add_paragraph(f"• {police_type}：{count}起")

        # 同比环比数据（如果有）
        if comparison_data:
            doc.add_paragraph()
            doc.add_paragraph('同比环比分析：')
            # 这里可以添加具体的同比环比分析内容

        doc.add_page_break()

    def _add_criminal_security_analysis(self, doc: Document, special_analysis: Dict[str, Any],
                                      llm_content: Dict[str, Any]):
        """添加刑事案件与治安案件深度分析"""
        doc.add_heading('二、刑事案件与治安案件深度分析', level=1)

        # 刑事案件分析
        if 'criminal_analysis' in special_analysis:
            doc.add_heading('2.1 刑事案件分析', level=2)
            criminal_data = special_analysis['criminal_analysis']
            doc.add_paragraph(f"刑事案件总数：{criminal_data.get('total_count', 0)}起")

            # LLM深度分析
            if 'criminal_deep_analysis' in llm_content:
                doc.add_paragraph(llm_content['criminal_deep_analysis'])

        # 治安案件分析
        if 'security_analysis' in special_analysis:
            doc.add_heading('2.2 治安案件分析', level=2)
            security_data = special_analysis['security_analysis']
            doc.add_paragraph(f"治安案件总数：{security_data.get('total_count', 0)}起")

            # LLM深度分析
            if 'security_deep_analysis' in llm_content:
                doc.add_paragraph(llm_content['security_deep_analysis'])

        doc.add_page_break()

    def _add_theft_analysis(self, doc: Document, special_analysis: Dict[str, Any],
                          llm_content: Dict[str, Any]):
        """添加盗窃案件深度分析"""
        doc.add_heading('三、盗窃案件深度分析', level=1)

        if 'theft_analysis' in special_analysis:
            theft_data = special_analysis['theft_analysis']
            doc.add_paragraph(f"盗窃案件总数：{theft_data.get('total_count', 0)}起")

            # 物品类型分析
            if 'theft_items_analysis' in llm_content:
                doc.add_heading('3.1 被盗物品类型分析', level=2)
                doc.add_paragraph(llm_content['theft_items_analysis'])

            # 场所分析
            if 'theft_location_analysis' in llm_content:
                doc.add_heading('3.2 发生场所分析', level=2)
                doc.add_paragraph(llm_content['theft_location_analysis'])

            # 深度分析
            if 'theft_deep_analysis' in llm_content:
                doc.add_heading('3.3 综合分析', level=2)
                doc.add_paragraph(llm_content['theft_deep_analysis'])

        doc.add_page_break()

    def _add_vice_analysis(self, doc: Document, special_analysis: Dict[str, Any],
                         llm_content: Dict[str, Any]):
        """添加涉黄与涉赌警情深度分析"""
        doc.add_heading('四、涉黄与涉赌警情深度分析', level=1)

        # 涉黄分析
        if 'prostitution_analysis' in special_analysis:
            doc.add_heading('4.1 涉黄警情分析', level=2)
            prostitution_data = special_analysis['prostitution_analysis']
            doc.add_paragraph(f"涉黄警情总数：{prostitution_data.get('total_count', 0)}起")

            if 'prostitution_deep_analysis' in llm_content:
                doc.add_paragraph(llm_content['prostitution_deep_analysis'])

            if 'prostitution_measures' in llm_content:
                doc.add_heading('4.1.1 防范措施建议', level=3)
                doc.add_paragraph(llm_content['prostitution_measures'])

        # 涉赌分析
        if 'gambling_analysis' in special_analysis:
            doc.add_heading('4.2 涉赌警情分析', level=2)
            gambling_data = special_analysis['gambling_analysis']
            doc.add_paragraph(f"涉赌警情总数：{gambling_data.get('total_count', 0)}起")

            if 'gambling_deep_analysis' in llm_content:
                doc.add_paragraph(llm_content['gambling_deep_analysis'])

            if 'gambling_measures' in llm_content:
                doc.add_heading('4.2.1 防范措施建议', level=3)
                doc.add_paragraph(llm_content['gambling_measures'])

        doc.add_page_break()

    def _add_fight_analysis(self, doc: Document, special_analysis: Dict[str, Any],
                          llm_content: Dict[str, Any]):
        """添加打架斗殴警情深度分析"""
        doc.add_heading('五、打架斗殴警情深度分析', level=1)

        if 'fight_analysis' in special_analysis:
            fight_data = special_analysis['fight_analysis']
            doc.add_paragraph(f"打架斗殴警情总数：{fight_data.get('total_count', 0)}起")

            # 场所分析
            if 'fight_location_analysis' in llm_content:
                doc.add_heading('5.1 发生场所分析', level=2)
                doc.add_paragraph(llm_content['fight_location_analysis'])

            # 深度分析
            if 'fight_deep_analysis' in llm_content:
                doc.add_heading('5.2 综合分析', level=2)
                doc.add_paragraph(llm_content['fight_deep_analysis'])

            # 防范措施
            if 'fight_measures' in llm_content:
                doc.add_heading('5.3 预防和处置措施建议', level=2)
                doc.add_paragraph(llm_content['fight_measures'])

        doc.add_page_break()

    def _add_charts_section(self, doc: Document, chart_paths: List[str]):
        """添加图表展示部分"""
        doc.add_heading('六、图表展示', level=1)

        for chart_path in chart_paths:
            if os.path.exists(chart_path):
                self._add_image_to_doc(doc, chart_path)
                doc.add_paragraph()

    def _add_comprehensive_title_page(self, doc: Document, time_range: str, fkdwdm: str = None):
        """添加全面分析报告标题页"""
        # 标题
        title = doc.add_heading('警情数据深度分析报告', level=0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 副标题
        subtitle = doc.add_paragraph(f'时间范围：{time_range}')
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER

        if fkdwdm:
            # 获取派出所名称
            from services.data_service import data_service
            station_name = data_service.get_police_station_name(fkdwdm)
            # 只显示派出所名称，不显示"派出所："前缀
            fkdw_info = doc.add_paragraph(station_name)
            fkdw_info.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 生成时间
        generate_time = doc.add_paragraph(f'生成时间：{datetime.now().strftime("%Y年%m月%d日 %H:%M")}')
        generate_time.alignment = WD_ALIGN_PARAGRAPH.CENTER

        doc.add_page_break()



    def _add_comprehensive_police_overview(self, doc: Document, overview_stats: Dict[str, Any], chart_paths: List[str]):
        """添加总警情概况（包含同比环比）"""
        doc.add_heading('一、总警情概况', level=1)

        # 创建表格
        table = doc.add_table(rows=1, cols=5)
        table.style = 'Table Grid'

        # 表头
        header_cells = table.rows[0].cells
        header_cells[0].text = '警情类型'
        header_cells[1].text = '当前数量'
        header_cells[2].text = '环比变化'
        header_cells[3].text = '同比变化'
        header_cells[4].text = '趋势分析'

        # 数据行
        police_types = [
            "总警情", "刑事案件", "治安案件", "盗窃案件", "诈骗案件",
            "通讯网络诈骗案件", "故意损毁财物案件", "殴打他人案件",
            "打架斗殴警情", "涉黄警情", "涉赌警情", "纠纷警情", "求助警情"
        ]

        for police_type in police_types:
            if police_type in overview_stats:
                data = overview_stats[police_type]
                row_cells = table.add_row().cells
                row_cells[0].text = police_type
                row_cells[1].text = str(data.get('current', 0))

                period_change = data.get('period_change', {})
                row_cells[2].text = f"{period_change.get('trend', '持平')} {abs(period_change.get('rate', 0)):.1f}%"

                year_change = data.get('year_change', {})
                row_cells[3].text = f"{year_change.get('trend', '持平')} {abs(year_change.get('rate', 0)):.1f}%"

                # 趋势分析
                if abs(period_change.get('rate', 0)) > 20 or abs(year_change.get('rate', 0)) > 20:
                    row_cells[4].text = "显著变化"
                elif abs(period_change.get('rate', 0)) > 10 or abs(year_change.get('rate', 0)) > 10:
                    row_cells[4].text = "明显变化"
                else:
                    row_cells[4].text = "平稳"

        # 添加相关图表
        doc.add_heading('1.1 24小时警情分布', level=2)
        self._add_chart_by_keyword(doc, chart_paths, ['24小时', 'hourly', '时间分布'])

        doc.add_heading('1.2 社区警情分布', level=2)
        self._add_chart_by_keyword(doc, chart_paths, ['社区', 'community', '地域分布'])

        doc.add_heading('1.3 警情类别分布', level=2)
        self._add_chart_by_keyword(doc, chart_paths, ['类别', 'category', '警情类型'])

        doc.add_page_break()

    def _add_comprehensive_criminal_security_analysis(self, doc: Document, special_analysis: Dict[str, Any], llm_content: Dict[str, Any], chart_paths: List[str]):
        """添加刑事案件与治安案件分析"""
        doc.add_heading('二、刑事案件与治安案件分析', level=1)

        # 2.1 基础统计概况
        doc.add_heading('2.1 基础统计概况', level=2)

        criminal_count = 0
        security_count = 0

        if 'criminal_analysis' in special_analysis:
            criminal_data = special_analysis['criminal_analysis']
            criminal_count = criminal_data.get('total_count', 0)

        if 'security_analysis' in special_analysis:
            security_data = special_analysis['security_analysis']
            security_count = security_data.get('total_count', 0)

        total_count = criminal_count + security_count

        if total_count > 0:
            doc.add_paragraph(f"本期共发生刑事案件{criminal_count}起，治安案件{security_count}起，合计{total_count}起。其中刑事案件占比{criminal_count/total_count*100:.1f}%，治安案件占比{security_count/total_count*100:.1f}%。")

        # 2.2 综合分析
        if 'criminal_security_deep_analysis' in llm_content:
            doc.add_heading('2.2 综合分析', level=2)
            doc.add_paragraph(llm_content['criminal_security_deep_analysis'])

        # 2.3 刑事案件专项分析
        if 'criminal_analysis' in llm_content and criminal_count > 0:
            doc.add_heading('2.3 刑事案件专项分析', level=2)
            doc.add_paragraph(llm_content['criminal_analysis'])

        # 2.4 治安案件专项分析
        if 'security_analysis' in llm_content and security_count > 0:
            doc.add_heading('2.4 治安案件专项分析', level=2)
            doc.add_paragraph(llm_content['security_analysis'])

        doc.add_page_break()

    def _add_comprehensive_theft_analysis(self, doc: Document, special_analysis: Dict[str, Any], llm_content: Dict[str, Any], chart_paths: List[str]):
        """添加盗窃案件分析"""
        doc.add_heading('三、盗窃案件分析', level=1)

        # 3.1 基础统计概况
        doc.add_heading('3.1 基础统计概况', level=2)
        if 'theft_analysis' in special_analysis:
            theft_data = special_analysis['theft_analysis']
            doc.add_paragraph(f"盗窃案件总数：{theft_data.get('total_count', 0)}起")

        # 3.2 物品与场所分析
        if 'theft_clustering' in llm_content:
            doc.add_heading('3.2 物品与场所分析', level=2)

            theft_clustering = llm_content['theft_clustering']
            if isinstance(theft_clustering, dict):
                # 处理字典格式的分析结果
                items = theft_clustering.get('items', {})
                locations = theft_clustering.get('locations', {})

                if items:
                    doc.add_paragraph("被盗物品类型统计：")
                    for item_type, count in sorted(items.items(), key=lambda x: x[1], reverse=True):
                        doc.add_paragraph(f"• {item_type}：{count}起", style='List Bullet')

                if locations:
                    doc.add_paragraph("案发场所类型统计：")
                    for location_type, count in sorted(locations.items(), key=lambda x: x[1], reverse=True):
                        doc.add_paragraph(f"• {location_type}：{count}起", style='List Bullet')
            else:
                # 处理字符串格式的分析结果（向后兼容）
                doc.add_paragraph("物品与场所分析结果：")
                doc.add_paragraph(str(theft_clustering))

            # 添加盗窃物品类型饼图
            self._add_chart_by_keyword(doc, chart_paths, ['盗窃物品', '物品类型', 'theft_items'])

        # 3.3 综合分析
        if 'theft_deep_analysis' in llm_content:
            doc.add_heading('3.3 综合分析', level=2)
            doc.add_paragraph(llm_content['theft_deep_analysis'])

        doc.add_page_break()

    def _add_comprehensive_vice_analysis(self, doc: Document, special_analysis: Dict[str, Any], llm_content: Dict[str, Any], chart_paths: List[str]):
        """添加涉黄与涉赌警情分析"""
        doc.add_heading('四、涉黄与涉赌警情分析', level=1)

        # 4.1 基础统计概况
        doc.add_heading('4.1 基础统计概况', level=2)

        prostitution_count = 0
        gambling_count = 0

        if 'prostitution_analysis' in special_analysis:
            prostitution_data = special_analysis['prostitution_analysis']
            prostitution_count = prostitution_data.get('total_count', 0)

        if 'gambling_analysis' in special_analysis:
            gambling_data = special_analysis['gambling_analysis']
            gambling_count = gambling_data.get('total_count', 0)

        total_count = prostitution_count + gambling_count

        if total_count > 0:
            doc.add_paragraph(f"本期共发生涉黄警情{prostitution_count}起，涉赌警情{gambling_count}起，合计{total_count}起。其中涉黄警情占比{prostitution_count/total_count*100:.1f}%，涉赌警情占比{gambling_count/total_count*100:.1f}%。")

        # 4.2 场所分析
        if 'vice_locations' in llm_content:
            doc.add_heading('4.2 场所分析', level=2)

            vice_locations = llm_content['vice_locations']
            if isinstance(vice_locations, dict):
                # 处理字典格式的分析结果
                locations = vice_locations.get('locations', {})

                if locations:
                    doc.add_paragraph("涉黄涉赌场所类型统计：")
                    for location_type, count in sorted(locations.items(), key=lambda x: x[1], reverse=True):
                        doc.add_paragraph(f"• {location_type}：{count}起", style='List Bullet')
                else:
                    doc.add_paragraph("暂无涉黄涉赌场所分析数据。")
            else:
                # 处理字符串格式的分析结果（向后兼容）
                doc.add_paragraph(str(vice_locations))

        # 4.3 综合分析
        if 'vice_deep_analysis' in llm_content:
            doc.add_heading('4.3 综合分析', level=2)
            doc.add_paragraph(llm_content['vice_deep_analysis'])

        # 4.4 时间分布分析
        doc.add_heading('4.4 涉黄警情时间分布', level=2)
        self._add_chart_by_keyword(doc, chart_paths, ['涉黄', 'prostitution', '涉黄时间'])

        doc.add_heading('4.5 涉赌警情时间分布', level=2)
        self._add_chart_by_keyword(doc, chart_paths, ['涉赌', 'gambling', '涉赌时间'])

        doc.add_page_break()

    def _add_comprehensive_fight_analysis(self, doc: Document, special_analysis: Dict[str, Any], llm_content: Dict[str, Any], chart_paths: List[str]):
        """添加打架斗殴警情分析"""
        doc.add_heading('五、打架斗殴警情分析', level=1)

        # 5.1 基础统计概况
        doc.add_heading('5.1 基础统计概况', level=2)
        if 'fight_analysis' in special_analysis:
            fight_data = special_analysis['fight_analysis']
            doc.add_paragraph(f"打架斗殴警情总数：{fight_data.get('total_count', 0)}起")

        # 5.2 场所分析
        if 'fight_locations' in llm_content:
            doc.add_heading('5.2 场所分析', level=2)

            fight_locations = llm_content['fight_locations']
            if isinstance(fight_locations, dict):
                # 处理字典格式的分析结果
                locations = fight_locations.get('locations', {})

                if locations:
                    doc.add_paragraph("打架斗殴场所类型统计：")
                    for location_type, count in sorted(locations.items(), key=lambda x: x[1], reverse=True):
                        doc.add_paragraph(f"• {location_type}：{count}起", style='List Bullet')
                else:
                    doc.add_paragraph("暂无打架斗殴场所分析数据。")
            else:
                # 处理字符串格式的分析结果（向后兼容）
                doc.add_paragraph(str(fight_locations))

        # 5.3 综合分析
        if 'fight_deep_analysis' in llm_content:
            doc.add_heading('5.3 综合分析', level=2)
            doc.add_paragraph(llm_content['fight_deep_analysis'])

        # 5.4 时间分布分析
        doc.add_heading('5.4 打架斗殴警情时间分布', level=2)
        self._add_chart_by_keyword(doc, chart_paths, ['打架斗殴', 'fight', '斗殴时间'])

        doc.add_page_break()

    def _add_comprehensive_recommendations(self, doc: Document, llm_content: Dict[str, Any]):
        """添加综合防范建议"""
        doc.add_heading('六、综合防范建议', level=1)

        if 'comprehensive_recommendations' in llm_content:
            doc.add_paragraph(llm_content['comprehensive_recommendations'])
        else:
            doc.add_paragraph("基于以上各类警情分析结果，建议采取综合性防范措施，加强重点场所管控，完善防控机制，提升整体治安水平。")

        doc.add_page_break()

    def _add_chart_by_keyword(self, doc: Document, chart_paths: List[str], keywords: List[str]):
        """根据关键词查找并添加图表"""
        # 关键词到英文文件名的映射
        keyword_to_filename = {
            '24小时': 'hourly_distribution.png',
            'hourly': 'hourly_distribution.png',
            '时间分布': 'hourly_distribution.png',
            '社区': 'community_distribution.png',
            'community': 'community_distribution.png',
            '地域分布': 'geographic_distribution.png',
            '类别': 'category_distribution.png',
            'category': 'category_distribution.png',
            '警情类型': 'category_distribution.png',
            '盗窃物品': 'theft_items_distribution.png',
            '物品类型': 'theft_items_distribution.png',
            'theft_items': 'theft_items_distribution.png',
            '涉黄': 'prostitution_time_distribution.png',
            'prostitution': 'prostitution_time_distribution.png',
            '涉黄时间': 'prostitution_time_distribution.png',
            '涉赌': 'gambling_time_distribution.png',
            'gambling': 'gambling_time_distribution.png',
            '涉赌时间': 'gambling_time_distribution.png',
            '打架斗殴': 'fight_time_distribution.png',
            'fight': 'fight_time_distribution.png',
            '斗殴时间': 'fight_time_distribution.png'
        }

        # 首先尝试通过关键词映射查找
        for keyword in keywords:
            target_filename = keyword_to_filename.get(keyword.lower())
            if target_filename:
                for chart_path in chart_paths:
                    if os.path.exists(chart_path) and os.path.basename(chart_path).lower() == target_filename.lower():
                        self._add_image_to_doc(doc, chart_path)
                        return

        # 如果映射查找失败，回退到原来的模糊匹配
        for chart_path in chart_paths:
            if os.path.exists(chart_path):
                chart_filename = os.path.basename(chart_path).lower()
                for keyword in keywords:
                    if keyword.lower() in chart_filename:
                        self._add_image_to_doc(doc, chart_path)
                        return

        # 如果没有找到匹配的图表，添加提示
        doc.add_paragraph("（相关图表将在数据充足时生成）")

    def _generate_report_filename(self, time_range: str, fkdwdm: str = None) -> str:
        """
        生成报告文件名
        命名规则：report_fkdwdm_年份_月份.docx
        如：report_330499510000_2025_01.docx
        """
        try:
            # 解析时间范围，提取日期部分
            # time_range 格式通常是 "2025-01-01 00:00:00 至 2025-01-31 23:59:59"
            import re
            from datetime import datetime

            date_pattern = r'(\d{4}-\d{2}-\d{2})'
            dates = re.findall(date_pattern, time_range)

            if len(dates) >= 2:
                start_date = dates[0]
                # 从开始日期提取年份和月份
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                year = start_dt.strftime('%Y')
                month = start_dt.strftime('%m')
            else:
                # 如果无法解析日期，使用当前日期
                current_dt = datetime.now()
                year = current_dt.strftime('%Y')
                month = current_dt.strftime('%m')

            # 生成文件名
            if fkdwdm:
                filename = f"report_{fkdwdm}_{year}_{month}.docx"
            else:
                filename = f"report_all_{year}_{month}.docx"

            logger.info(f"生成报告文件名: {filename}")
            return filename

        except Exception as e:
            logger.error(f"生成报告文件名失败: {e}")
            # 如果生成失败，使用默认命名
            from datetime import datetime
            return f"report_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"


# 全局报告服务实例
report_service = ReportService()
