"""
警情数据模型
"""
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, List, Dict, Any


class PoliceDataModel(BaseModel):
    """警情数据模型"""
    bjsj: Optional[datetime] = Field(None, description="报警时间")
    county: Optional[str] = Field(None, description="区县")
    community: Optional[str] = Field(None, description="社区")
    police_situation_category: Optional[str] = Field(None, description="警情类别(一级)")
    police_situation_type: Optional[str] = Field(None, description="警情类型(二级)")
    cause: Optional[str] = Field(None, description="警情小类/原因(三级)")
    cjqk: Optional[str] = Field(None, description="警情详细最终反馈情况")
    jqgjz: Optional[str] = Field(None, description="关键词字段")
    jqxz: Optional[str] = Field(None, description="警情详址")
    fkdwdm: Optional[str] = Field(None, description="派出所代码")
    bjlbmc: Optional[str] = Field(None, description="报警类别名称")
    bjlxmc: Optional[str] = Field(None, description="报警类型名称")


class ReportRequest(BaseModel):
    """报告生成请求模型"""
    start_time: str = Field(..., description="开始时间", example="2023-01-01 00:00:00")
    end_time: str = Field(..., description="结束时间", example="2023-01-31 23:59:59")
    fkdwdm: Optional[str] = Field(None, description="派出所代码，用于筛选特定派出所的数据", example="110101")


class ReportResponse(BaseModel):
    """报告生成响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    report_path: Optional[str] = Field(None, description="报告文件路径")
    data_summary: Optional[Dict[str, Any]] = Field(None, description="数据摘要")


class DataAnalysisResult(BaseModel):
    """数据分析结果模型"""
    total_records: int = Field(..., description="总记录数")
    time_range: Dict[str, str] = Field(..., description="时间范围")
    county_distribution: Dict[str, int] = Field(..., description="区县分布")
    category_distribution: Dict[str, int] = Field(..., description="警情类别分布")
    type_distribution: Dict[str, int] = Field(..., description="警情类型分布")
    cause_distribution: Dict[str, int] = Field(..., description="警情小类分布")
    cjqk_distribution: Dict[str, int] = Field(..., description="出警情况分布")
    fkdwdm_distribution: Dict[str, int] = Field(..., description="派出所分布")
    hourly_distribution: Dict[str, int] = Field(..., description="小时分布")
    weekly_distribution: Dict[str, int] = Field(..., description="周分布")


class ChartConfig(BaseModel):
    """图表配置模型"""
    chart_type: str = Field(..., description="图表类型")
    title: str = Field(..., description="图表标题")
    data: Dict[str, Any] = Field(..., description="图表数据")
    style: Optional[Dict[str, Any]] = Field(None, description="图表样式")
