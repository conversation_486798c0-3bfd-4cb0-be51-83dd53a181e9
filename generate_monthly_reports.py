#!/usr/bin/env python3
"""
批量生成月报告脚本
生成2025年1-6月每个月的月报告，并保存到数据库
"""
import sys
import os
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any
import calendar

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.data_service import data_service
from services.analysis_engine import analysis_engine
from services.chart_service import enhanced_chart_service
from services.llm_service import llm_service
from services.report_service import report_service
from database.connection import db_manager
from utils.logger import setup_logger
from loguru import logger
from sqlalchemy import text

# 初始化日志
setup_logger()

class MonthlyReportGenerator:
    """月报告生成器"""

    def __init__(self, mode='batch'):
        """
        初始化月报告生成器

        Args:
            mode: 运行模式
                - 'batch': 批量生成模式（生成2025年1-6月）
                - 'monthly': 月度定时模式（生成上个月报告）
                - 'custom': 自定义模式（需要手动指定年月）
        """
        self.fkdwdm_list = [
            '330499510000',
            '330499520000',
            '330499530000',
            '330499540000',
            '330499550000'
        ]
        self.mode = mode

        if mode == 'batch':
            # 批量模式：生成2025年1-6月
            self.year = 2025
            self.months = list(range(1, 7))  # 1-6月
        elif mode == 'monthly':
            # 月度模式：生成上个月报告
            self._set_last_month()
        else:
            # 自定义模式：需要手动设置
            self.year = None
            self.months = []

    def _set_last_month(self):
        """设置为上个月的年月"""
        from datetime import datetime, timedelta

        # 获取当前日期
        today = datetime.now()

        # 计算上个月
        if today.month == 1:
            # 如果当前是1月，上个月是去年12月
            self.year = today.year - 1
            self.months = [12]
        else:
            # 其他情况，上个月是当年的前一个月
            self.year = today.year
            self.months = [today.month - 1]

        logger.info(f"月度模式：将生成 {self.year} 年 {self.months[0]} 月的报告")

    def set_custom_period(self, year: int, months: list):
        """设置自定义时间段"""
        self.year = year
        self.months = months
        logger.info(f"自定义模式：将生成 {year} 年 {months} 月的报告")
        
    def get_month_date_range(self, year: int, month: int) -> tuple:
        """获取指定年月的日期范围"""
        # 获取月份的第一天
        start_date = datetime(year, month, 1)
        
        # 获取月份的最后一天
        last_day = calendar.monthrange(year, month)[1]
        end_date = datetime(year, month, last_day, 23, 59, 59)
        
        start_time = start_date.strftime('%Y-%m-%d %H:%M:%S')
        end_time = end_date.strftime('%Y-%m-%d %H:%M:%S')
        
        return start_time, end_time
    
    async def generate_single_report(self, fkdwdm: str, year: int, month: int) -> Dict[str, Any]:
        """生成单个报告"""
        try:
            logger.info(f"开始生成报告: 派出所={fkdwdm}, 年月={year}-{month:02d}")
            
            # 获取时间范围
            start_time, end_time = self.get_month_date_range(year, month)
            logger.info(f"时间范围: {start_time} 至 {end_time}")
            
            # 1. 获取数据
            logger.info("正在获取警情数据...")
            df = data_service.get_police_data(start_time, end_time, fkdwdm)
            
            if df.empty:
                logger.warning(f"派出所 {fkdwdm} 在 {year}-{month:02d} 月未找到数据")
                return {
                    'success': False,
                    'message': '未找到数据',
                    'fkdwdm': fkdwdm,
                    'year': year,
                    'month': month
                }
            
            logger.info(f"获取到 {len(df)} 条数据")
            
            # 2. 数据分析
            logger.info("正在进行数据分析...")
            analysis_result = analysis_engine.comprehensive_analysis(df)
            
            # 2.1 同比环比分析
            logger.info("正在进行同比环比分析...")
            comparison_analysis = data_service.get_multi_period_analysis(
                start_time, end_time, fkdwdm
            )
            analysis_result["comparison_analysis"] = comparison_analysis
            
            # 2.2 专项分析
            logger.info("正在进行专项分析...")
            special_analysis = analysis_engine.comprehensive_special_analysis(
                data_service, start_time, end_time, fkdwdm
            )
            
            # 2.3 获取总警情概况统计
            logger.info("正在获取总警情概况...")
            overview_stats = data_service.get_police_overview_with_comparison(
                start_time, end_time, fkdwdm
            )
            
            # 3. 生成图表
            logger.info("正在生成图表...")
            chart_paths = enhanced_chart_service.generate_required_charts(
                analysis_result, data_service, start_time, end_time, fkdwdm
            )
            
            # 4. 生成LLM分析内容
            logger.info("正在生成LLM深度分析...")
            time_range = f"{start_time} 至 {end_time}"
            llm_content = await llm_service.generate_comprehensive_llm_analysis(
                data_service, start_time, end_time, fkdwdm
            )
            
            # 5. 生成Word报告
            logger.info("正在生成Word报告...")
            report_path = report_service.generate_comprehensive_report(
                analysis_result, chart_paths, llm_content, special_analysis,
                overview_stats, time_range, fkdwdm
            )
            
            logger.info(f"报告生成成功: {report_path}")
            
            return {
                'success': True,
                'message': '报告生成成功',
                'fkdwdm': fkdwdm,
                'year': year,
                'month': month,
                'report_path': report_path,
                'data_summary': {
                    'total_records': analysis_result['basic_stats']['total_records'],
                    'date_range': analysis_result['basic_stats']['date_range']
                }
            }
            
        except Exception as e:
            logger.error(f"生成报告失败: 派出所={fkdwdm}, 年月={year}-{month:02d}, 错误={e}")
            return {
                'success': False,
                'message': f'报告生成失败: {str(e)}',
                'fkdwdm': fkdwdm,
                'year': year,
                'month': month
            }
    
    def extract_filename(self, local_path: str) -> str:
        """提取文件名"""
        try:
            # 获取文件名
            filename = os.path.basename(local_path)

            logger.info(f"提取文件名: {local_path} -> {filename}")
            return filename

        except Exception as e:
            logger.error(f"提取文件名失败: {e}")
            return local_path  # 降级返回原路径

    def save_report_to_database(self, result: Dict[str, Any]) -> bool:
        """保存报告信息到数据库"""
        try:
            if not result['success']:
                logger.warning(f"跳过保存失败的报告: {result}")
                return False

            # 检查表是否存在，如果不存在则创建
            self.ensure_reports_table_exists()

            # 提取文件名
            filename = self.extract_filename(result['report_path'])

            # 使用事务来确保数据一致性
            with db_manager.get_session() as session:
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 删除相同fkdwdm、year、month的旧记录（如果存在）
                delete_query = """
                DELETE FROM p_pcs_reports
                WHERE fkdwdm = :fkdwdm AND year = :year AND month = :month
                """

                delete_params = {
                    'fkdwdm': result['fkdwdm'],
                    'year': result['year'],
                    'month': result['month']
                }

                deleted_result = session.execute(text(delete_query), delete_params)
                deleted_count = deleted_result.rowcount

                if deleted_count > 0:
                    logger.info(f"删除了 {deleted_count} 条旧记录: {result['fkdwdm']}-{result['year']}-{result['month']:02d}")

                # 插入新记录
                insert_query = """
                INSERT INTO p_pcs_reports (fkdwdm, year, month, name, created_at)
                VALUES (:fkdwdm, :year, :month, :name, :created_at)
                """

                insert_params = {
                    'fkdwdm': result['fkdwdm'],
                    'year': result['year'],
                    'month': result['month'],
                    'name': filename,
                    'created_at': current_time
                }

                session.execute(text(insert_query), insert_params)
                session.commit()

                logger.info(f"保存报告记录: {result['fkdwdm']}-{result['year']}-{result['month']:02d}")
                logger.info(f"文件名: {filename}")

                return True

        except Exception as e:
            logger.error(f"保存报告信息到数据库失败: {e}")
            return False
    
    def ensure_reports_table_exists(self):
        """确保报告表存在"""
        try:
            with db_manager.get_session() as session:
                create_table_query = """
                CREATE TABLE IF NOT EXISTS p_pcs_reports (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    fkdwdm VARCHAR(50) NOT NULL COMMENT '派出所代码',
                    year INT NOT NULL COMMENT '年份',
                    month INT NOT NULL COMMENT '月份',
                    name VARCHAR(500) NOT NULL COMMENT '报告文件名',
                    created_at DATETIME NOT NULL COMMENT '创建时间',
                    INDEX idx_fkdwdm_year_month (fkdwdm, year, month),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='派出所月报告记录表'
                """

                session.execute(text(create_table_query))
                session.commit()
                logger.info("报告表检查/创建完成")

        except Exception as e:
            logger.error(f"创建报告表失败: {e}")
            raise
    
    async def generate_all_reports(self):
        """生成所有报告"""
        logger.info("开始批量生成月报告...")
        logger.info(f"派出所代码: {self.fkdwdm_list}")
        logger.info(f"年份: {self.year}")
        logger.info(f"月份: {self.months}")

        # 测试数据库连接
        if not db_manager.test_connection():
            logger.error("数据库连接失败，无法继续")
            return

        total_reports = len(self.fkdwdm_list) * len(self.months)
        current_count = 0
        success_count = 0

        results = []

        start_time = datetime.now()

        for fkdwdm in self.fkdwdm_list:
            for month in self.months:
                current_count += 1
                logger.info(f"进度: {current_count}/{total_reports} - 正在处理 {fkdwdm}-{self.year}-{month:02d}")

                # 生成报告
                result = await self.generate_single_report(fkdwdm, self.year, month)
                results.append(result)

                # 保存到数据库
                if self.save_report_to_database(result):
                    success_count += 1

                # 计算预估剩余时间
                elapsed = datetime.now() - start_time
                if current_count > 0:
                    avg_time_per_report = elapsed.total_seconds() / current_count
                    remaining_reports = total_reports - current_count
                    estimated_remaining = remaining_reports * avg_time_per_report
                    logger.info(f"预估剩余时间: {estimated_remaining/60:.1f} 分钟")

                # 添加延迟避免系统过载
                await asyncio.sleep(3)

        # 输出总结
        total_time = datetime.now() - start_time
        logger.info(f"批量生成完成!")
        logger.info(f"总计: {total_reports}, 成功: {success_count}, 失败: {total_reports - success_count}")
        logger.info(f"总耗时: {total_time.total_seconds()/60:.1f} 分钟")

        # 输出详细结果
        logger.info("详细结果:")
        for result in results:
            status = "✓" if result['success'] else "✗"
            logger.info(f"{status} {result['fkdwdm']}-{result['year']}-{result['month']:02d}: {result['message']}")

        # 输出数据库保存情况
        self.show_database_summary()

    def show_database_summary(self):
        """显示数据库中的报告摘要"""
        try:
            with db_manager.get_session() as session:
                # 查询当前数据库中的报告记录
                query = """
                SELECT fkdwdm, year, month, name, created_at
                FROM p_pcs_reports
                WHERE year = :year
                ORDER BY fkdwdm, month
                """

                result = session.execute(text(query), {'year': self.year}).fetchall()

                if result:
                    logger.info(f"数据库中 {self.year} 年的报告记录:")
                    for row in result:
                        logger.info(f"  {row[0]}-{row[1]}-{row[2]:02d}: {row[3]} (创建时间: {row[4]})")
                else:
                    logger.warning(f"数据库中未找到 {self.year} 年的报告记录")

        except Exception as e:
            logger.error(f"查询数据库摘要失败: {e}")

def check_if_monthly_run_day() -> bool:
    """检查今天是否是月度运行日（每月3号）"""
    from datetime import datetime
    today = datetime.now()
    return today.day == 3

async def main():
    """主函数"""
    import sys

    try:
        # 解析命令行参数
        if len(sys.argv) > 1:
            mode = sys.argv[1]
        else:
            # 默认模式：如果是3号则运行月度模式，否则运行批量模式
            if check_if_monthly_run_day():
                mode = 'monthly'
                logger.info("检测到今天是3号，自动切换到月度模式")
            else:
                mode = 'batch'

        logger.info("=" * 60)

        if mode == 'batch':
            logger.info("批量月报告生成脚本启动（2025年1-6月）")
            generator = MonthlyReportGenerator(mode='batch')
        elif mode == 'monthly':
            logger.info("月度报告生成脚本启动（上个月）")
            generator = MonthlyReportGenerator(mode='monthly')
        elif mode == 'custom':
            logger.info("自定义报告生成脚本启动")
            generator = MonthlyReportGenerator(mode='custom')

            # 自定义模式需要额外参数
            if len(sys.argv) < 4:
                logger.error("自定义模式需要指定年份和月份")
                logger.info("用法: python generate_monthly_reports.py custom 2025 1,2,3")
                return

            year = int(sys.argv[2])
            months = [int(m.strip()) for m in sys.argv[3].split(',')]
            generator.set_custom_period(year, months)
        else:
            logger.error(f"未知的运行模式: {mode}")
            logger.info("支持的模式: batch, monthly, custom")
            return

        logger.info("=" * 60)

        await generator.generate_all_reports()

        logger.info("=" * 60)
        logger.info("月报告生成脚本完成")
        logger.info("=" * 60)

    except KeyboardInterrupt:
        logger.warning("用户中断了脚本执行")
    except Exception as e:
        logger.error(f"脚本执行出错: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
