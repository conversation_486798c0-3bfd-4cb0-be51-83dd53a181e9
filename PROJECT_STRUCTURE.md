# 项目结构说明

## 📁 目录结构

```
110DataReports/
├── app/                    # 应用主模块
│   ├── __init__.py
│   └── api.py             # FastAPI路由和接口定义
├── database/              # 数据库模块
│   ├── __init__.py
│   └── connection.py      # 数据库连接管理
├── models/                # 数据模型
│   ├── __init__.py
│   └── police_data.py     # 警情数据模型
├── services/              # 业务服务层
│   ├── __init__.py
│   ├── analysis_engine.py # 数据分析引擎
│   ├── chart_service.py   # 图表生成服务
│   ├── data_service.py    # 数据处理服务
│   ├── llm_service.py     # LLM集成服务
│   └── report_service.py  # 报告生成服务
├── utils/                 # 工具模块
│   ├── __init__.py
│   └── logger.py          # 日志配置
├── tests/                 # 测试模块
│   ├── __init__.py
│   └── test_api.py        # API测试
├── static/                # 静态资源（可选）
│   ├── redoc.standalone.js
│   ├── swagger-ui-bundle.js
│   └── swagger-ui.css
├── charts/                # 生成的图表文件
├── reports/               # 生成的报告文件
├── logs/                  # 日志文件
├── config.py              # 配置文件
├── main.py                # 应用入口
├── start.py               # 启动脚本
├── requirements.txt       # Python依赖
├── download_swagger_assets.py  # 静态资源下载脚本
├── prepare_offline.py     # 离线部署准备脚本
├── setup_chinese_fonts.py # 中文字体配置脚本
├── .env                   # 环境变量配置
├── .gitignore            # Git忽略文件
└── README.md             # 项目说明
```

## 📋 核心文件说明

### 主要文件
- **main.py**: FastAPI应用入口，包含路由配置和API文档
- **start.py**: 启动脚本，检查环境和依赖
- **config.py**: 系统配置管理

### 业务模块
- **app/api.py**: API接口定义，包含报告生成、数据预览等接口
- **services/**: 核心业务逻辑
  - `data_service.py`: 数据查询和处理（已优化案件分类查询）
  - `analysis_engine.py`: 数据分析和统计
  - `chart_service.py`: 图表生成（支持中文）
  - `report_service.py`: Word报告生成（已优化深度分析章节）
  - `llm_service.py`: LLM深度分析服务（专注于深度分析功能）

### 数据层
- **database/connection.py**: 数据库连接池管理
- **models/police_data.py**: 警情数据模型定义

### 工具脚本
- **download_swagger_assets.py**: 下载离线API文档所需的静态资源
- **prepare_offline.py**: 创建离线部署包
- **setup_chinese_fonts.py**: 配置中文字体支持

## 🚀 快速开始

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **配置环境**
   ```bash
   cp .env.example .env
   # 编辑.env文件配置数据库和LLM服务
   ```

3. **启动服务**
   ```bash
   python start.py
   ```

4. **访问服务**
   - API文档: http://localhost:1998/docs
   - 健康检查: http://localhost:1998/health

## 📊 主要功能

- ✅ 警情数据分析报告生成
- ✅ 多维度数据统计（时间、地域、类别）
- ✅ 中文图表生成（无乱码）
- ✅ 同比环比分析
- ✅ LLM智能文本生成
- ✅ Word文档导出
- ✅ 离线部署支持

## 🔧 配置说明

主要配置项在`.env`文件中：
- 数据库连接配置
- LLM服务配置
- 应用运行配置

详细配置说明请参考README.md文件。
