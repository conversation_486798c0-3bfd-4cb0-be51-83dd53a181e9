"""
图表生成服务 - 基于visualizer.py重构
"""
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Tuple, Optional
import os
from loguru import logger
from datetime import datetime
from config import settings


class PoliceDataChartService:
    """警情数据图表生成服务 - 基于visualizer.py重构"""

    def __init__(self, output_dir: str = None):
        """
        初始化图表服务

        Args:
            output_dir: 图表输出目录
        """
        self.output_dir = output_dir or settings.chart_output_dir
        self._setup_matplotlib()
        self._create_output_dir()

        # 英文文件名到中文显示名的映射表
        self.chart_name_mapping = {
            'hourly_distribution.png': '24小时警情分布图',
            'category_distribution.png': '警情类别分布图',
            'geographic_distribution.png': '地域分布图',
            'county_ranking.png': '区县排名图',
            'community_distribution.png': '社区分布图',
            'theft_items_distribution.png': '盗窃物品类型分布图',
            'theft_locations_distribution.png': '盗窃案件场所分布图',
            'vice_locations_distribution.png': '涉黄涉赌场所分布图',
            'fight_locations_distribution.png': '打架斗殴场所分布图',
            'prostitution_time_distribution.png': '涉黄警情时间分布图',
            'gambling_time_distribution.png': '涉赌警情时间分布图',
            'fight_time_distribution.png': '打架斗殴警情时间分布图',
            'criminal_security_comparison.png': '刑事治安案件对比图',
            'monthly_trend.png': '月度趋势图',
            'weekly_distribution.png': '周分布图',
            'comparison_chart.png': '同比环比对比图'
        }

    def _setup_matplotlib(self):
        """设置matplotlib中文字体"""
        # 优化字体设置，优先使用Docker容器中安装的字体
        plt.rcParams['font.sans-serif'] = [
            'WenQuanYi Zen Hei',    # 文泉驿正黑体 (Docker容器中安装)
            'WenQuanYi Micro Hei',  # 文泉驿微米黑 (Docker容器中安装)
            'Noto Sans CJK SC',     # Noto Sans 中文 (Docker容器中安装)
            'Noto Sans CJK',        # Noto Sans CJK (Docker容器中安装)
            'Microsoft YaHei',      # 微软雅黑 (Windows默认)
            'SimHei',               # 黑体
            'SimSun',               # 宋体
            'KaiTi',                # 楷体
            'Arial Unicode MS',     # Arial Unicode MS
            'DejaVu Sans',          # Linux默认
            'sans-serif'            # 系统默认
        ]
        plt.rcParams['axes.unicode_minus'] = False

        # 设置字体大小（增大字体）
        plt.rcParams['font.size'] = 14
        plt.rcParams['axes.titlesize'] = 18
        plt.rcParams['axes.labelsize'] = 16
        plt.rcParams['xtick.labelsize'] = 14
        plt.rcParams['ytick.labelsize'] = 14
        plt.rcParams['legend.fontsize'] = 14

        # 设置matplotlib样式，兼容不同版本
        try:
            plt.style.use('seaborn-v0_8')
        except OSError:
            try:
                plt.style.use('seaborn')
            except OSError:
                # 如果seaborn样式不可用，使用默认样式
                plt.style.use('default')
                # 手动设置一些美观的参数
                plt.rcParams['figure.facecolor'] = 'white'
                plt.rcParams['axes.facecolor'] = 'white'
                plt.rcParams['axes.edgecolor'] = 'gray'
                plt.rcParams['grid.color'] = 'lightgray'
                plt.rcParams['grid.alpha'] = 0.7

        logger.info("matplotlib中文字体配置完成")

    def _ensure_chinese_font(self):
        """确保中文字体设置"""
        # 每次绘图前重新设置字体，确保中文显示正常
        plt.rcParams['font.sans-serif'] = [
            'WenQuanYi Zen Hei', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC',
            'Microsoft YaHei', 'SimHei', 'SimSun', 'sans-serif'
        ]
        plt.rcParams['axes.unicode_minus'] = False

    def _create_output_dir(self):
        """创建输出目录"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            logger.info(f"创建图表输出目录: {self.output_dir}")

    def get_chart_chinese_name(self, english_filename: str) -> str:
        """
        根据英文文件名获取中文显示名

        Args:
            english_filename: 英文文件名（包含.png扩展名）

        Returns:
            中文显示名，如果找不到映射则返回去掉扩展名的英文文件名
        """
        return self.chart_name_mapping.get(english_filename, english_filename.replace('.png', ''))

    def _generate_english_filename(self, chinese_title: str) -> str:
        """
        根据中文标题生成英文文件名

        Args:
            chinese_title: 中文标题

        Returns:
            英文文件名（包含.png扩展名）
        """
        # 中文标题到英文文件名的映射
        title_to_filename = {
            '24小时警情分布图': 'hourly_distribution.png',
            '警情类别分布图': 'category_distribution.png',
            '警情类别分布': 'category_distribution.png',
            '地域分布图': 'geographic_distribution.png',
            '区县排名图': 'county_ranking.png',
            '社区分布图': 'community_distribution.png',
            '盗窃物品类型分布': 'theft_items_distribution.png',
            '盗窃案件场所类型分布': 'theft_locations_distribution.png',
            '涉黄涉赌场所类型分布': 'vice_locations_distribution.png',
            '打架斗殴场所类型分布': 'fight_locations_distribution.png',
            '涉黄警情时间分布': 'prostitution_time_distribution.png',
            '涉赌警情时间分布': 'gambling_time_distribution.png',
            '打架斗殴警情时间分布': 'fight_time_distribution.png',
            '刑事治安案件对比': 'criminal_security_comparison.png',
            '月度趋势': 'monthly_trend.png',
            '周分布': 'weekly_distribution.png',
            '同比环比对比': 'comparison_chart.png'
        }

        # 查找匹配的英文文件名
        for chinese_key, english_filename in title_to_filename.items():
            if chinese_key in chinese_title:
                return english_filename

        # 如果没有找到匹配，使用默认命名规则
        # 移除特殊字符并转换为英文
        safe_title = chinese_title.replace('/', '_').replace('\\', '_').replace(':', '_')
        return f"{safe_title}.png"

    def _get_color_palette(self):
        """获取颜色调色板"""
        return [
            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf',
            '#aec7e8', '#ffbb78', '#98df8a', '#ff9896', '#c5b0d5',
            '#c49c94', '#f7b6d3', '#c7c7c7', '#dbdb8d', '#9edae5'
        ]

    def _get_extended_color_palette(self, num_colors: int):
        """获取扩展颜色调色板"""
        base_colors = self._get_color_palette()

        if num_colors <= len(base_colors):
            return base_colors[:num_colors]

        # 如果需要更多颜色，生成额外的颜色
        import matplotlib.cm as cm
        import numpy as np

        # 使用matplotlib的颜色映射生成更多颜色
        additional_colors = cm.Set3(np.linspace(0, 1, num_colors - len(base_colors)))
        additional_hex = ['#%02x%02x%02x' % (int(r*255), int(g*255), int(b*255))
                         for r, g, b, a in additional_colors]

        return base_colors + additional_hex



    def create_hourly_distribution_chart(self, hourly_data: Dict[str, int]) -> str:
        """创建小时分布图表"""
        self._ensure_chinese_font()

        # 调试信息：打印原始数据
        logger.info(f"24小时分布原始数据: {hourly_data}")

        # 数据预处理 - 改进数据处理逻辑
        hours = list(range(24))
        counts = []

        for hour in hours:
            # 尝试多种键格式
            count = 0
            possible_keys = [
                str(hour),           # "0", "1", "2", ...
                f"{hour:02d}",       # "00", "01", "02", ...
                f"{hour:02d}时",     # "00时", "01时", "02时", ...
                f"{hour}时",         # "0时", "1时", "2时", ...
                hour                 # 0, 1, 2, ... (整数)
            ]

            for key in possible_keys:
                if key in hourly_data:
                    count = hourly_data[key]
                    break

            counts.append(count)

        # 调试信息：打印处理后的数据
        logger.info(f"24小时分布处理后数据: hours={hours}, counts={counts}")
        logger.info(f"总计数量: {sum(counts)}")

        # 创建图表（更大的尺寸）
        fig, ax = plt.subplots(figsize=(18, 12))

        # 绘制折线图（使用更粗的线条和更明显的标记）
        colors = self._get_color_palette()
        ax.plot(hours, counts, marker='o', linewidth=4, markersize=10, color=colors[0],
                markerfacecolor='white', markeredgewidth=3, markeredgecolor=colors[0])
        ax.fill_between(hours, counts, alpha=0.4, color=colors[0])

        # 设置标签和标题（使用更大更明显的字体）
        ax.set_xlabel('小时', fontsize=20, fontweight='bold')
        ax.set_ylabel('警情数量（起）', fontsize=20, fontweight='bold')
        ax.set_title('24小时警情分布趋势图', fontsize=24, fontweight='bold', pad=30)

        # 设置x轴（显示所有小时）
        ax.set_xlim(0, 23)
        ax.set_xticks(range(0, 24, 1))
        ax.set_xticklabels([f'{h}点' for h in range(0, 24, 1)], fontsize=14, fontweight='bold', rotation=45)

        # 设置y轴
        max_count = max(counts) if counts else 1
        ax.set_ylim(0, max_count * 1.15)
        ax.tick_params(axis='y', labelsize=16, labelcolor='black', width=2)

        # 添加数值标签（在所有点显示）
        for i, count in enumerate(counts):
            if count > 0:  # 只在有数据的点显示数值
                ax.annotate(f'{count}', (i, count), textcoords="offset points",
                           xytext=(0,15), ha='center', fontsize=12, fontweight='bold',
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

        # 美化图表
        ax.grid(True, alpha=0.4, linewidth=1.5)
        ax.set_facecolor('#f8f9fa')

        # 设置边框
        for spine in ax.spines.values():
            spine.set_linewidth(2)
            spine.set_color('#333333')

        plt.tight_layout()

        # 保存图表 - 使用英文文件名
        filename = os.path.join(self.output_dir, 'hourly_distribution.png')
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"24小时分布图表创建完成，保存到: {filename}")
        return filename





    def generate_pie_chart(self, data: Dict[str, int], title: str,
                          filename: str = None, show_all: bool = False) -> str:
        """生成饼图"""
        try:
            self._ensure_chinese_font()

            # 数据预处理
            labels = list(data.keys())
            values = list(data.values())

            # 根据参数决定是否显示全部数据
            if not show_all and len(labels) > 8:
                # 限制显示项目数量，其余归为"其他"
                top_items = sorted(zip(labels, values), key=lambda x: x[1], reverse=True)[:7]
                other_sum = sum(values) - sum([item[1] for item in top_items])

                labels = [item[0] for item in top_items] + ['其他']
                values = [item[1] for item in top_items] + [other_sum]
            else:
                # 显示全部数据，按数量排序
                sorted_items = sorted(zip(labels, values), key=lambda x: x[1], reverse=True)
                labels = [item[0] for item in sorted_items]
                values = [item[1] for item in sorted_items]

            # 创建图表（根据数据量调整大小）
            fig_size = (12, 10) if len(labels) > 8 else (10, 8)
            fig, ax = plt.subplots(figsize=fig_size)

            # 获取颜色（扩展颜色调色板）
            colors = self._get_extended_color_palette(len(labels))

            # 绘制饼图
            wedges, texts, autotexts = ax.pie(
                values,
                labels=labels,
                autopct='%1.1f%%',
                startangle=90,
                colors=colors,
                textprops={'fontsize': 12, 'fontweight': 'bold'}
            )

            # 设置标题（使用更大的字体）
            ax.set_title(title, fontsize=22, fontweight='bold', pad=30)

            # 调整文字大小（增大字体）
            for text in texts:
                text.set_fontsize(14)
            for autotext in autotexts:
                autotext.set_fontsize(12)
                autotext.set_color('white')
                autotext.set_fontweight('bold')

            plt.tight_layout()

            # 保存图表
            if not filename:
                # 根据标题生成英文文件名
                filename = self._generate_english_filename(title)

            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()

            logger.info(f"饼图生成成功: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"饼图生成失败: {e}")
            raise

    def generate_bar_chart(self, data: Dict[str, int], title: str,
                          xlabel: str = "", ylabel: str = "数量（起）",
                          filename: str = None) -> str:
        """生成柱状图"""
        try:
            self._ensure_chinese_font()

            # 数据预处理
            items = sorted(data.items(), key=lambda x: x[1], reverse=True)

            # 限制显示数量
            if len(items) > 15:
                items = items[:15]

            labels = [item[0] for item in items]
            values = [item[1] for item in items]

            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 8))

            # 获取颜色
            colors = self._get_color_palette()
            bars = ax.bar(range(len(labels)), values, color=colors[0])

            # 设置标签（使用更大的字体）
            ax.set_xlabel(xlabel, fontsize=16)
            ax.set_ylabel(ylabel, fontsize=16)
            ax.set_title(title, fontsize=20, fontweight='bold', pad=20)

            # 设置x轴标签
            ax.set_xticks(range(len(labels)))
            ax.set_xticklabels(labels, rotation=45, ha='right', fontsize=14)
            ax.tick_params(axis='y', labelsize=14)

            # 在柱子上显示数值
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
                       f'{value}', ha='center', va='bottom', fontsize=12)

            # 网格
            ax.grid(axis='y', alpha=0.3)

            plt.tight_layout()

            # 保存图表
            if not filename:
                # 根据标题生成英文文件名
                filename = self._generate_english_filename(title)

            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()

            logger.info(f"柱状图生成成功: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"柱状图生成失败: {e}")
            raise

    def generate_line_chart(self, data: Dict[str, int], title: str,
                           xlabel: str = "", ylabel: str = "警情数量（起）",
                           filename: str = None) -> str:
        """生成折线图（美化版）"""
        try:
            self._ensure_chinese_font()

            # 数据预处理 - 特别处理24小时数据
            if any('时' in str(key) for key in data.keys()):
                # 24小时数据处理
                hours_data = {}
                for key, value in data.items():
                    # 提取小时数
                    if '时' in str(key):
                        hour = int(str(key).replace('时', '').replace(':', ''))
                        hours_data[hour] = value

                # 确保所有24小时都有数据
                full_hours_data = {}
                for h in range(24):
                    full_hours_data[h] = hours_data.get(h, 0)

                labels = [f'{h}点' for h in range(24)]
                values = [full_hours_data[h] for h in range(24)]
                x_positions = list(range(24))
            else:
                # 其他数据处理
                if isinstance(list(data.keys())[0], str) and '-' in list(data.keys())[0]:
                    items = sorted(data.items(), key=lambda x: x[0])
                else:
                    items = sorted(data.items())

                labels = [item[0] for item in items]
                values = [item[1] for item in items]
                x_positions = list(range(len(labels)))

            # 创建图表（更大的尺寸）
            fig, ax = plt.subplots(figsize=(16, 10))

            # 获取颜色
            colors = self._get_color_palette()

            # 绘制折线图（更粗的线条和更大的标记）
            ax.plot(x_positions, values, marker='o', linewidth=4, markersize=8,
                   color=colors[0], markerfacecolor='white', markeredgewidth=3,
                   markeredgecolor=colors[0])
            ax.fill_between(x_positions, values, alpha=0.3, color=colors[0])

            # 设置标签（使用更大的字体）
            ax.set_xlabel(xlabel, fontsize=18, fontweight='bold')
            ax.set_ylabel(ylabel, fontsize=18, fontweight='bold')
            ax.set_title(title, fontsize=22, fontweight='bold', pad=30)

            # 设置x轴标签
            ax.set_xticks(x_positions)

            # 根据数据类型调整x轴标签显示
            if any('时' in str(key) for key in data.keys()):
                # 24小时数据：显示所有小时
                ax.set_xticklabels(labels, rotation=45, ha='right', fontsize=14, fontweight='bold')
            else:
                # 其他数据：根据标签数量调整显示
                if len(labels) > 20:
                    step = len(labels) // 10
                    ax.set_xticklabels([labels[i] if i % step == 0 else ''
                                      for i in range(len(labels))], rotation=45, ha='right', fontsize=14)
                else:
                    ax.set_xticklabels(labels, rotation=45, ha='right', fontsize=14)

            # 设置y轴
            max_value = max(values) if values else 1
            ax.set_ylim(0, max_value * 1.15)
            ax.tick_params(axis='y', labelsize=16, labelcolor='black', width=2)

            # 添加数值标签
            for i, value in enumerate(values):
                if value > 0:
                    ax.annotate(f'{value}', (x_positions[i], value), textcoords="offset points",
                               xytext=(0,10), ha='center', fontsize=11, fontweight='bold',
                               bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

            # 美化图表
            ax.grid(True, alpha=0.4, linewidth=1.5)
            ax.set_facecolor('#f8f9fa')

            # 设置边框
            for spine in ax.spines.values():
                spine.set_linewidth(2)
                spine.set_color('#333333')

            plt.tight_layout()

            # 保存图表
            if not filename:
                # 根据标题生成英文文件名
                filename = self._generate_english_filename(title)

            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()

            logger.info(f"折线图生成成功: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"折线图生成失败: {e}")
            raise

    def generate_comparison_chart(self, current_data: Dict[str, Any],
                                comparison_data: Dict[str, Any],
                                title: str, filename: str = None) -> str:
        """生成同比环比对比图表"""
        try:
            self._ensure_chinese_font()

            # 准备数据
            metrics = list(current_data.keys())
            current_values = list(current_data.values())
            comparison_values = [comparison_data.get(key, 0) for key in metrics]

            if not metrics:
                raise ValueError("No comparison data available")

            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 8))

            x = np.arange(len(metrics))
            width = 0.35

            # 获取颜色
            colors = self._get_color_palette()

            # 绘制对比柱状图
            bars1 = ax.bar(x - width/2, current_values, width,
                          label='当前期间', color=colors[0])
            bars2 = ax.bar(x + width/2, comparison_values, width,
                          label='对比期间', color=colors[1])

            # 设置标签和标题
            ax.set_xlabel('指标', fontsize=12)
            ax.set_ylabel('数值', fontsize=12)
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            ax.set_xticks(x)
            ax.set_xticklabels(metrics)
            ax.legend()

            # 在柱子上显示数值
            for bars in [bars1, bars2]:
                for bar in bars:
                    height = bar.get_height()
                    if height > 0:
                        ax.text(bar.get_x() + bar.get_width()/2., height + max(max(current_values), max(comparison_values))*0.01,
                               f'{height:.1f}', ha='center', va='bottom', fontsize=9)

            # 网格
            ax.grid(axis='y', alpha=0.3)

            plt.tight_layout()

            # 保存图表
            if not filename:
                # 根据标题生成英文文件名
                filename = self._generate_english_filename(title)

            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()

            logger.info(f"对比图表生成成功: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"对比图表生成失败: {e}")
            raise







    def generate_required_charts(self, analysis_results: Dict[str, Any],
                                data_service, start_time: str, end_time: str,
                                fkdwdm: Optional[str] = None) -> List[str]:
        """生成所有必需的图表"""
        try:
            chart_paths = []

            # 1. 24小时警情分布折线图
            if 'time_analysis' in analysis_results:
                hourly_data = analysis_results['time_analysis'].get('hourly_distribution', {})
                if hourly_data:
                    chart_path = self.create_hourly_distribution_chart(hourly_data)
                    chart_paths.append(chart_path)

            # 2. 社区警情分布饼图（显示全部社区）
            if 'geographic_analysis' in analysis_results:
                community_data = analysis_results['geographic_analysis'].get('community_distribution', {})
                if community_data:
                    chart_path = self.generate_pie_chart(
                        community_data,
                        "社区警情分布",
                        "社区警情分布饼图.png",
                        show_all=True  # 显示全部社区
                    )
                    chart_paths.append(chart_path)

            # 3. 警情类别分布柱状图
            if 'category_analysis' in analysis_results:
                category_data = analysis_results['category_analysis'].get('category_distribution', {})
                if category_data:
                    chart_path = self.generate_bar_chart(
                        category_data,
                        "警情类别分布",
                        "警情类别",
                        "警情数量（起）",
                        "警情类别分布柱状图.png"
                    )
                    chart_paths.append(chart_path)

            # 4. 打架斗殴警情分布
            fight_df = data_service.get_specific_police_data(start_time, end_time, "打架斗殴警情", fkdwdm)
            if not fight_df.empty:
                fight_hourly = fight_df.groupby('hour').size().to_dict()
                fight_hourly_formatted = {f"{h:02d}时": count for h, count in fight_hourly.items()}
                chart_path = self.generate_line_chart(
                    fight_hourly_formatted,
                    "打架斗殴警情24小时分布",
                    "时间",
                    "警情数量（起）",
                    "打架斗殴警情分布.png"
                )
                chart_paths.append(chart_path)

            # 5. 涉赌警情分布
            gambling_df = data_service.get_specific_police_data(start_time, end_time, "涉赌警情", fkdwdm)
            if not gambling_df.empty:
                gambling_hourly = gambling_df.groupby('hour').size().to_dict()
                gambling_hourly_formatted = {f"{h:02d}时": count for h, count in gambling_hourly.items()}
                chart_path = self.generate_line_chart(
                    gambling_hourly_formatted,
                    "涉赌警情24小时分布",
                    "时间",
                    "警情数量（起）",
                    "涉赌警情分布.png"
                )
                chart_paths.append(chart_path)

            # 6. 涉黄警情分布
            prostitution_df = data_service.get_specific_police_data(start_time, end_time, "涉黄警情", fkdwdm)
            if not prostitution_df.empty:
                prostitution_hourly = prostitution_df.groupby('hour').size().to_dict()
                prostitution_hourly_formatted = {f"{h:02d}时": count for h, count in prostitution_hourly.items()}
                chart_path = self.generate_line_chart(
                    prostitution_hourly_formatted,
                    "涉黄警情24小时分布",
                    "时间",
                    "警情数量（起）",
                    "涉黄警情分布.png"
                )
                chart_paths.append(chart_path)

            # 7. 盗窃物品类型饼图
            theft_df = data_service.get_specific_police_data(start_time, end_time, "盗窃案件", fkdwdm)
            if not theft_df.empty:
                # 从cjqk字段提取物品类型（简化版本）
                theft_items = self._extract_theft_items_simple(theft_df)
                if theft_items:
                    chart_path = self.generate_theft_items_chart(theft_items)
                    if chart_path:
                        chart_paths.append(chart_path)

            logger.info(f"成功生成 {len(chart_paths)} 个必需图表")
            return chart_paths

        except Exception as e:
            logger.error(f"生成必需图表失败: {e}")
            return []

    def generate_theft_items_chart(self, theft_items_data: Dict[str, int]) -> str:
        """生成盗窃物品类型饼图"""
        try:
            if not theft_items_data:
                logger.warning("无盗窃物品数据，跳过图表生成")
                return ""

            chart_path = self.generate_pie_chart(
                theft_items_data,
                "盗窃物品类型分布",
                "盗窃物品类型饼图.png",
                show_all=True  # 显示全量数据，包括"其他"
            )

            logger.info("盗窃物品类型饼图生成完成")
            return chart_path

        except Exception as e:
            logger.error(f"生成盗窃物品类型饼图失败: {e}")
            return ""

    def _extract_theft_items_simple(self, theft_df) -> Dict[str, int]:
        """简化的盗窃物品类型提取方法"""
        items_count = {}

        # 简单的关键词匹配
        keywords = {
            "手机": ["手机", "iPhone", "华为", "小米", "OPPO", "vivo", "苹果手机"],
            "电动车": ["电动车", "电瓶车", "摩托车", "电动自行车"],
            "现金": ["现金", "钱", "人民币", "零钱", "钱包"],
            "首饰": ["首饰", "项链", "戒指", "手表", "金银"],
            "电脑": ["电脑", "笔记本", "台式机", "平板"],
            "自行车": ["自行车", "单车", "山地车"],
            "其他": []
        }

        for _, row in theft_df.iterrows():
            cjqk = str(row.get('cjqk', ''))
            found = False

            for item_type, kws in keywords.items():
                if item_type == "其他":
                    continue

                for kw in kws:
                    if kw in cjqk:
                        items_count[item_type] = items_count.get(item_type, 0) + 1
                        found = True
                        break

                if found:
                    break

            if not found:
                items_count["其他"] = items_count.get("其他", 0) + 1

        return items_count


# 创建增强的图表服务类别名，保持向后兼容
class EnhancedChartService(PoliceDataChartService):
    """增强图表服务 - 向后兼容"""
    pass


    def create_location_analysis_charts(self, llm_results: Dict[str, Any]) -> List[str]:
        """为LLM场所分析结果生成图表"""
        try:
            chart_paths = []

            # 1. 涉黄涉赌场所分布图
            if 'vice_locations' in llm_results:
                vice_locations = llm_results['vice_locations']
                if isinstance(vice_locations, dict) and 'locations' in vice_locations:
                    locations_data = vice_locations['locations']
                    if locations_data:
                        chart_path = self.generate_pie_chart(
                            locations_data,
                            "涉黄涉赌场所类型分布",
                            "vice_locations_distribution.png",
                            show_all=True
                        )
                        chart_paths.append(chart_path)
                        logger.info("涉黄涉赌场所分布图生成完成")

            # 2. 打架斗殴场所分布图
            if 'fight_locations' in llm_results:
                fight_locations = llm_results['fight_locations']
                if isinstance(fight_locations, dict) and 'locations' in fight_locations:
                    locations_data = fight_locations['locations']
                    if locations_data:
                        chart_path = self.generate_pie_chart(
                            locations_data,
                            "打架斗殴场所类型分布",
                            "fight_locations_distribution.png",
                            show_all=True
                        )
                        chart_paths.append(chart_path)
                        logger.info("打架斗殴场所分布图生成完成")

            # 3. 盗窃案件场所分布图
            if 'theft_clustering' in llm_results:
                theft_clustering = llm_results['theft_clustering']
                if isinstance(theft_clustering, dict) and 'locations' in theft_clustering:
                    locations_data = theft_clustering['locations']
                    if locations_data:
                        chart_path = self.generate_pie_chart(
                            locations_data,
                            "盗窃案件场所类型分布",
                            "theft_locations_distribution.png",
                            show_all=True
                        )
                        chart_paths.append(chart_path)
                        logger.info("盗窃案件场所分布图生成完成")

            logger.info(f"场所分析图表生成完成，共生成 {len(chart_paths)} 个图表")
            return chart_paths

        except Exception as e:
            logger.error(f"生成场所分析图表失败: {e}")
            return []


# 全局图表服务实例
chart_service = PoliceDataChartService()
enhanced_chart_service = EnhancedChartService()
