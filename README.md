# 警情数据分析报告生成系统

基于数据库警情数据，使用大数据处理+LLM生成多维分析报告（Word格式）的后端接口系统。

## 功能特性

- 🔍 **多维数据分析**：时间分布、地域分布、警情类别等多维度分析
- 📊 **专业图表生成**：饼图、柱状图、趋势图等美观图表
- 📈 **同比环比分析**：自动计算同比环比变化率，提供趋势对比分析
- 🤖 **AI智能分析**：集成Qwen3-235B-A22B模型生成专业分析文本
- 📄 **Word报告生成**：自动生成格式规范的专业Word报告
- 🚀 **RESTful API**：提供完整的API接口服务
- 📈 **实时数据处理**：基于真实数据库数据，禁止数据捏造

## 系统架构

```
├── app/                    # FastAPI应用
├── database/              # 数据库连接模块
├── models/                # 数据模型
├── services/              # 业务服务层
│   ├── data_service.py    # 数据查询和预处理
│   ├── analysis_engine.py # 数据分析引擎
│   ├── chart_service.py   # 图表生成服务
│   ├── llm_service.py     # LLM集成服务
│   └── report_service.py  # Word报告生成
├── utils/                 # 工具函数
├── tests/                 # 测试模块
├── reports/               # 报告输出目录
├── charts/                # 图表输出目录
└── logs/                  # 日志目录
```

## 环境要求

- Python 3.8+
- MySQL数据库
- 内存：建议4GB以上
- 磁盘：建议2GB以上可用空间

## 快速开始

### 在线环境部署

#### 1. 克隆项目

```bash
git clone <repository-url>
cd 110DataReports
```

#### 2. 安装依赖

```bash
pip install -r requirements.txt
```

#### 3. 配置环境

复制 `.env.example` 为 `.env` 并修改配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库和LLM服务信息。

#### 4. 启动服务

**方式一：使用启动脚本（推荐）**
```bash
python start.py
```

**方式二：直接启动**
```bash
python main.py
```

#### 5. 访问服务

- 服务地址：http://localhost:1998
- API文档：http://localhost:1998/docs
- 健康检查：http://localhost:1998/health

### 离线环境部署

#### 1. 准备离线部署包

在有网络的机器上运行：

```bash
# 下载静态资源（可选，用于完整的离线部署）
python download_swagger_assets.py

# 创建完整的离线部署包
python prepare_offline.py
```

#### 2. 离线机器部署

1. 将项目文件复制到离线机器
2. 安装Python依赖：
   ```bash
   pip install -r requirements.txt
   ```
3. 配置环境变量（编辑.env文件）
4. 启动服务：
   ```bash
   python start.py
   ```

#### 3. 离线环境访问

- 服务地址：http://localhost:8000
- API文档：http://localhost:8000/docs （简化版API文档）
- 健康检查：http://localhost:8000/health

> **注意**：系统使用简化的API文档界面，无需复杂的静态资源即可正常使用。

## API接口

### 生成报告

```http
POST /api/v1/generate-report
Content-Type: application/json

{
  "start_time": "2023-01-01 00:00:00",
  "end_time": "2023-01-31 23:59:59"
}
```

### 下载报告

```http
GET /api/v1/download-report/{filename}
```

### 数据预览

```http
GET /api/v1/data-preview?start_time=2023-01-01 00:00:00&end_time=2023-01-02 00:00:00&limit=10
```

### 系统状态

```http
GET /api/v1/system-status
```

## 配置说明

### 数据库配置

```env
DB_HOST=***********
DB_PORT=3306
DB_USER=jkga
DB_PASSWORD=Company_user20250331
DB_NAME=dsu-f-tagservice
DB_TABLE=p_translation_feedback_data
```

### LLM服务配置

```env
LLM_URL=http://************:8889
LLM_API_KEY=sk-HBE4QnJ910R7ntO1421c91CdA56a40589aB1B72f3524C460
LLM_MODEL=Qwen3-235B-A22B
```

## 数据字段说明

| 字段名 | 描述 | 类型 |
|--------|------|------|
| BJSJ | 报警时间 | DATETIME |
| county | 区县 | VARCHAR |
| community | 社区 | VARCHAR |
| police_situation_category | 警情类别 | VARCHAR |
| police_situation_type | 警情类型 | VARCHAR |
| cause | 警情小类 | VARCHAR |
| cjqk | 出警情况 | VARCHAR |

## 报告内容

生成的Word报告包含以下内容：

1. **执行摘要**：数据概况和关键发现
2. **数据概览**：基础统计信息
3. **时间分布分析**：24小时和周分布规律
4. **地域分布分析**：区县和社区分布特征
5. **警情类别分析**：三级分类统计分析
6. **同比环比分析**：与上期和去年同期的对比分析
7. **综合分析**：LLM生成的专业分析文本
8. **关键洞察**：数据驱动的重要发现
9. **结论与建议**：基于分析的工作建议

## 测试

运行测试：

```bash
pytest tests/
```

## 日志

系统日志保存在 `logs/` 目录：

- `app.log`：应用日志
- `error.log`：错误日志

## 故障排除

### 1. 数据库连接失败

- 检查数据库配置信息
- 确认数据库服务正常运行
- 检查网络连接

### 2. LLM服务调用失败

- 检查LLM服务地址和API密钥
- 确认LLM服务正常运行
- 检查网络连接

### 3. 图表生成失败或中文乱码

**中文字体配置：**
```bash
# 运行中文字体配置脚本
python setup_chinese_fonts.py
```

**手动配置方法：**

Windows系统：
- 系统通常已包含微软雅黑、黑体、宋体等中文字体
- 如有问题，请检查系统字体是否正常

Linux系统：
```bash
# Ubuntu/Debian
sudo apt install fonts-wqy-zenhei fonts-wqy-microhei
sudo apt install fonts-noto-cjk fonts-noto-cjk-extra

# CentOS/RHEL
sudo yum install wqy-zenhei-fonts wqy-microhei-fonts
sudo yum install google-noto-cjk-fonts
```

**验证字体配置：**
- 运行 `python setup_chinese_fonts.py` 进行测试
- 查看生成的 `chinese_font_test.png` 确认中文显示正常

### 4. Word文档生成失败

- 检查python-docx库安装
- 确认输出目录权限

## 开发指南

### 添加新的分析维度

1. 在 `services/analysis_engine.py` 中添加分析逻辑
2. 在 `services/chart_service.py` 中添加对应图表
3. 在 `services/report_service.py` 中添加报告内容

### 自定义图表样式

修改 `services/chart_service.py` 中的样式配置。

### 扩展LLM功能

在 `services/llm_service.py` 中添加新的提示词模板和处理逻辑。

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。
