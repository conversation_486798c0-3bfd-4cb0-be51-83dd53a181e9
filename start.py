"""
系统启动脚本
"""
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误：需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✓ Python版本: {sys.version}")


def check_dependencies():
    """检查关键依赖是否已安装"""
    print("正在检查依赖包...")
    try:
        import fastapi
        import uvicorn
        import pandas
        import numpy
        print("✓ 关键依赖包检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("请先安装依赖包:")
        print("  pip install -r requirements.txt")
        return False


def create_env_file():
    """创建环境配置文件"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("正在创建.env配置文件...")
        env_file.write_text(env_example.read_text(encoding='utf-8'), encoding='utf-8')
        print("✓ .env文件已创建，请根据需要修改配置")


def create_directories():
    """创建必要目录"""
    directories = ["reports", "charts", "logs"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✓ 目录结构创建完成")


def start_server():
    """启动服务器"""
    print("正在启动服务器...")
    print("服务器将在 http://localhost:1998 启动")
    print("API文档地址: http://localhost:1998/docs")
    print("按 Ctrl+C 停止服务器")
    
    try:
        subprocess.run([sys.executable, "main.py"])
    except KeyboardInterrupt:
        print("\n服务器已停止")


def main():
    """主函数"""
    print("=" * 50)
    print("警情数据分析报告生成系统")
    print("=" * 50)
    
    # 检查Python版本
    check_python_version()
    
    # 创建环境配置文件
    create_env_file()
    
    # 创建目录
    create_directories()

    # 检查依赖
    if not check_dependencies():
        print("\n请先安装依赖包后再启动服务器")
        return

    # 启动服务器
    start_server()


if __name__ == "__main__":
    main()
