#!/usr/bin/env python3
"""
设置月度报告生成的定时任务
每月3号凌晨2点自动生成上个月的报告
"""
import os
import sys
import subprocess
from pathlib import Path

def get_script_path():
    """获取脚本的绝对路径"""
    current_dir = Path(__file__).parent.absolute()
    script_path = current_dir / "generate_monthly_reports.py"
    return str(script_path)

def get_python_path():
    """获取Python解释器路径"""
    return sys.executable

def create_cron_job():
    """创建cron定时任务"""
    script_path = get_script_path()
    python_path = get_python_path()
    
    # 切换到脚本目录
    script_dir = os.path.dirname(script_path)
    
    # cron任务命令：每月3号凌晨2点执行
    cron_command = f"0 2 3 * * cd {script_dir} && {python_path} {script_path} monthly >> /var/log/monthly_reports.log 2>&1"
    
    print("准备添加的cron任务:")
    print(f"时间: 每月3号凌晨2点")
    print(f"命令: {cron_command}")
    print()
    
    # 检查是否已存在相同的任务
    try:
        result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
        current_crontab = result.stdout
        
        if 'generate_monthly_reports.py monthly' in current_crontab:
            print("⚠️  检测到已存在月度报告生成任务")
            response = input("是否要替换现有任务? (y/N): ")
            if response.lower() != 'y':
                print("取消操作")
                return
            
            # 移除现有任务
            lines = current_crontab.split('\n')
            filtered_lines = [line for line in lines if 'generate_monthly_reports.py monthly' not in line]
            new_crontab = '\n'.join(filtered_lines)
        else:
            new_crontab = current_crontab
    
    except subprocess.CalledProcessError:
        # 如果没有现有的crontab，创建新的
        new_crontab = ""
    
    # 添加新任务
    if new_crontab and not new_crontab.endswith('\n'):
        new_crontab += '\n'
    new_crontab += cron_command + '\n'
    
    # 写入crontab
    try:
        process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE, text=True)
        process.communicate(input=new_crontab)
        
        if process.returncode == 0:
            print("✅ 定时任务设置成功!")
            print(f"📅 将在每月3号凌晨2点自动生成上个月的报告")
            print(f"📝 日志文件: /var/log/monthly_reports.log")
        else:
            print("❌ 定时任务设置失败")
            
    except Exception as e:
        print(f"❌ 设置定时任务时出错: {e}")

def show_current_cron():
    """显示当前的cron任务"""
    try:
        result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
        if result.returncode == 0:
            print("当前的cron任务:")
            print("-" * 50)
            print(result.stdout)
        else:
            print("没有找到现有的cron任务")
    except Exception as e:
        print(f"查看cron任务时出错: {e}")

def remove_cron_job():
    """移除月度报告的cron任务"""
    try:
        result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
        current_crontab = result.stdout
        
        if 'generate_monthly_reports.py monthly' not in current_crontab:
            print("没有找到月度报告生成任务")
            return
        
        # 移除相关任务
        lines = current_crontab.split('\n')
        filtered_lines = [line for line in lines if 'generate_monthly_reports.py monthly' not in line]
        new_crontab = '\n'.join(filtered_lines)
        
        # 写入crontab
        process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE, text=True)
        process.communicate(input=new_crontab)
        
        if process.returncode == 0:
            print("✅ 月度报告定时任务已移除")
        else:
            print("❌ 移除定时任务失败")
            
    except Exception as e:
        print(f"移除定时任务时出错: {e}")

def test_monthly_run():
    """测试月度运行"""
    script_path = get_script_path()
    python_path = get_python_path()
    script_dir = os.path.dirname(script_path)
    
    print("🧪 测试月度报告生成...")
    print(f"执行命令: cd {script_dir} && {python_path} {script_path} monthly")
    print()
    
    try:
        os.chdir(script_dir)
        result = subprocess.run([python_path, script_path, 'monthly'], 
                              capture_output=False, text=True)
        
        if result.returncode == 0:
            print("✅ 测试运行成功")
        else:
            print("❌ 测试运行失败")
            
    except Exception as e:
        print(f"测试运行时出错: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("月度报告生成定时任务设置工具")
    print("=" * 60)
    print()
    
    while True:
        print("请选择操作:")
        print("1. 设置定时任务（每月3号凌晨2点）")
        print("2. 查看当前定时任务")
        print("3. 移除定时任务")
        print("4. 测试月度运行")
        print("5. 退出")
        print()
        
        choice = input("请输入选项 (1-5): ").strip()
        
        if choice == '1':
            create_cron_job()
        elif choice == '2':
            show_current_cron()
        elif choice == '3':
            remove_cron_job()
        elif choice == '4':
            test_monthly_run()
        elif choice == '5':
            print("退出")
            break
        else:
            print("无效选项，请重新选择")
        
        print()
        input("按回车键继续...")
        print()

if __name__ == "__main__":
    main()
