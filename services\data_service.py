"""
数据查询和预处理服务
"""
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger
from database.connection import db_manager
from models.police_data import DataAnalysisResult
from config import settings

# Import analysis_engine to avoid circular import
def get_analysis_engine():
    from services.analysis_engine import analysis_engine
    return analysis_engine


class DataService:
    """数据服务类"""
    
    def __init__(self):
        self.table_name = settings.db_table
    
    def get_police_data(self, start_time: str, end_time: str, fkdwdm: Optional[str] = None) -> pd.DataFrame:
        """获取指定时间范围的警情数据"""
        try:
            # 构建基础查询
            query = f"""
            SELECT
                bjsj,
                county,
                community,
                police_situation_category,
                police_situation_type,
                cause,
                cjqk,
                jqgjz,
                jqxz,
                fkdwdm,
                bjlbmc,
                bjlxmc
            FROM {self.table_name}
            WHERE bjsj >= :start_time
            AND bjsj <= :end_time
            """

            params = {
                'start_time': start_time,
                'end_time': end_time
            }

            # 如果提供了fkdwdm参数，添加筛选条件
            if fkdwdm is not None:
                query += " AND fkdwdm = :fkdwdm"
                params['fkdwdm'] = fkdwdm

            query += " ORDER BY bjsj"
            
            fkdwdm_info = f", 派出所代码: {fkdwdm}" if fkdwdm else ""
            logger.info(f"查询警情数据: {start_time} 到 {end_time}{fkdwdm_info}")
            
            # 执行查询
            results = db_manager.execute_query(query, params)
            
            if not results:
                logger.warning("未查询到数据")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(results)
            
            # 数据预处理
            df = self._preprocess_data(df)
            
            logger.info(f"成功获取 {len(df)} 条警情数据")
            return df
            
        except Exception as e:
            logger.error(f"获取警情数据失败: {e}")
            raise
    
    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        try:
            # 处理时间字段
            if 'bjsj' in df.columns:
                df['bjsj'] = pd.to_datetime(df['bjsj'], errors='coerce')
                
                # 提取时间特征
                df['hour'] = df['bjsj'].dt.hour
                df['day_of_week'] = df['bjsj'].dt.dayofweek
                df['day_name'] = df['bjsj'].dt.day_name()
                df['date'] = df['bjsj'].dt.date
            
            # 清理空值和异常值
            df = df.dropna(subset=['bjsj'])
            
            # 字符串字段去除前后空格
            string_columns = ['county', 'community', 'police_situation_category',
                            'police_situation_type', 'cause', 'cjqk', 'jqgjz', 'jqxz', 'fkdwdm', 'bjlbmc', 'bjlxmc']
            
            for col in string_columns:
                if col in df.columns:
                    df[col] = df[col].astype(str).str.strip()
                    df[col] = df[col].replace(['nan', 'None', ''], None)
            
            logger.info("数据预处理完成")
            return df
            
        except Exception as e:
            logger.error(f"数据预处理失败: {e}")
            raise
    
    def analyze_data(self, df: pd.DataFrame) -> DataAnalysisResult:
        """分析数据并生成统计结果"""
        try:
            if df.empty:
                raise ValueError("数据为空，无法进行分析")
            
            # 基础统计
            total_records = len(df)
            time_range = {
                'start': df['bjsj'].min().strftime('%Y-%m-%d %H:%M:%S'),
                'end': df['bjsj'].max().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 各维度分布统计
            county_dist = self._get_distribution(df, 'county')
            category_dist = self._get_distribution(df, 'police_situation_category')
            type_dist = self._get_distribution(df, 'police_situation_type')
            cause_dist = self._get_distribution(df, 'cause')
            cjqk_dist = self._get_distribution(df, 'cjqk')
            fkdwdm_dist = self._get_distribution(df, 'fkdwdm')
            
            # 时间分布统计
            hourly_dist = self._get_hourly_distribution(df)
            weekly_dist = self._get_weekly_distribution(df)
            
            result = DataAnalysisResult(
                total_records=total_records,
                time_range=time_range,
                county_distribution=county_dist,
                category_distribution=category_dist,
                type_distribution=type_dist,
                cause_distribution=cause_dist,
                cjqk_distribution=cjqk_dist,
                fkdwdm_distribution=fkdwdm_dist,
                hourly_distribution=hourly_dist,
                weekly_distribution=weekly_dist
            )
            
            logger.info(f"数据分析完成，共分析 {total_records} 条记录")
            return result
            
        except Exception as e:
            logger.error(f"数据分析失败: {e}")
            raise
    
    def _get_distribution(self, df: pd.DataFrame, column: str) -> Dict[str, int]:
        """获取指定列的分布统计"""
        if column not in df.columns:
            return {}
        
        # 过滤空值并统计
        dist = df[column].dropna().value_counts().to_dict()
        
        # 转换为字符串键
        return {str(k): int(v) for k, v in dist.items()}
    
    def _get_hourly_distribution(self, df: pd.DataFrame) -> Dict[str, int]:
        """获取小时分布"""
        if 'hour' not in df.columns:
            return {}
        
        hourly_counts = df['hour'].value_counts().sort_index()
        return {f"{hour:02d}时": int(count) for hour, count in hourly_counts.items()}
    
    def _get_weekly_distribution(self, df: pd.DataFrame) -> Dict[str, int]:
        """获取周分布"""
        if 'day_name' not in df.columns:
            return {}
        
        # 中文星期映射
        day_mapping = {
            'Monday': '周一',
            'Tuesday': '周二', 
            'Wednesday': '周三',
            'Thursday': '周四',
            'Friday': '周五',
            'Saturday': '周六',
            'Sunday': '周日'
        }
        
        weekly_counts = df['day_name'].value_counts()
        return {day_mapping.get(day, day): int(count) for day, count in weekly_counts.items()}
    
    def get_custom_dimension_analysis(self, df: pd.DataFrame, 
                                    categories: List[str], 
                                    types: List[str], 
                                    causes: List[str]) -> Dict[str, Any]:
        """自定义维度组合分析"""
        try:
            # 筛选符合条件的数据
            filtered_df = df.copy()
            
            if categories:
                filtered_df = filtered_df[
                    filtered_df['police_situation_category'].isin(categories)
                ]
            
            if types:
                filtered_df = filtered_df[
                    filtered_df['police_situation_type'].isin(types)
                ]
            
            if causes:
                filtered_df = filtered_df[
                    filtered_df['cause'].isin(causes)
                ]
            
            # 统计结果
            result = {
                'total_count': len(filtered_df),
                'percentage': len(filtered_df) / len(df) * 100 if len(df) > 0 else 0,
                'county_distribution': self._get_distribution(filtered_df, 'county'),
                'hourly_distribution': self._get_hourly_distribution(filtered_df),
                'cjqk_distribution': self._get_distribution(filtered_df, 'cjqk')
            }
            
            return result

        except Exception as e:
            logger.error(f"自定义维度分析失败: {e}")
            raise

    def get_comparison_data(self, start_time: str, end_time: str,
                          comparison_type: str = "period", fkdwdm: Optional[str] = None) -> pd.DataFrame:
        """获取对比期间数据

        Args:
            start_time: 当前期间开始时间
            end_time: 当前期间结束时间
            comparison_type: 对比类型 ("period"=环比, "year"=同比)
        """
        try:
            from datetime import datetime, timedelta
            from dateutil.relativedelta import relativedelta

            current_start = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
            current_end = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')

            # 计算时间跨度
            time_span = current_end - current_start

            if comparison_type == "year":
                # 同比：去年同期
                comparison_start = current_start - relativedelta(years=1)
                comparison_end = current_end - relativedelta(years=1)
            else:
                # 环比：上一个相同时间段
                comparison_end = current_start - timedelta(seconds=1)
                comparison_start = comparison_end - time_span

            comparison_start_str = comparison_start.strftime('%Y-%m-%d %H:%M:%S')
            comparison_end_str = comparison_end.strftime('%Y-%m-%d %H:%M:%S')

            logger.info(f"获取{comparison_type}对比数据: {comparison_start_str} 到 {comparison_end_str}")

            # 获取对比期间数据
            comparison_df = self.get_police_data(comparison_start_str, comparison_end_str, fkdwdm)

            return comparison_df

        except Exception as e:
            logger.error(f"获取对比数据失败: {e}")
            return pd.DataFrame()

    def get_specific_police_data(self, start_time: str, end_time: str,
                               police_type: str, fkdwdm: Optional[str] = None) -> pd.DataFrame:
        """获取特定类型的警情数据"""
        try:
            # 构建基础查询
            base_query = f"""
            SELECT
                bjsj,
                county,
                community,
                police_situation_category,
                police_situation_type,
                cause,
                cjqk,
                jqgjz,
                jqxz,
                fkdwdm,
                bjlbmc,
                bjlxmc
            FROM {self.table_name}
            WHERE bjsj >= :start_time
            AND bjsj <= :end_time
            """

            params = {
                'start_time': start_time,
                'end_time': end_time
            }

            # 根据警情类型添加特定筛选条件（严格按照指定的查询条件）
            if police_type == "刑事案件":
                base_query += " AND police_situation_category = '刑事案件'"
            elif police_type == "治安案件":
                base_query += " AND police_situation_category = '行政(治安)案件'"
            elif police_type == "盗窃案件":
                base_query += """ AND (
                    (police_situation_category = '刑事案件' OR police_situation_category = '行政(治安)案件')
                    AND (police_situation_type = '盗窃' OR police_situation_type = '盗窃案')
                )"""
            elif police_type == "诈骗案件":
                base_query += """ AND (
                    (police_situation_category = '刑事案件' OR police_situation_category = '行政(治安)案件')
                    AND (police_situation_type = '诈骗' OR police_situation_type = '诈骗案')
                )"""
            elif police_type == "通讯网络诈骗案件":
                base_query += """ AND (
                    (police_situation_category = '刑事案件' OR police_situation_category = '行政(治安)案件')
                    AND (police_situation_type = '诈骗' OR police_situation_type = '诈骗案')
                    AND (cause = '网络诈骗' OR cause = '电信诈骗')
                )"""
            elif police_type == "故意损毁财物案件":
                base_query += """ AND (
                    (police_situation_category = '刑事案件' OR police_situation_category = '行政(治安)案件')
                    AND police_situation_type = '故意损毁财物'
                )"""
            elif police_type == "殴打他人案件":
                base_query += """ AND (
                    (police_situation_category = '刑事案件' OR police_situation_category = '行政(治安)案件')
                    AND police_situation_type = '殴打他人'
                )"""
            elif police_type == "涉黄警情":
                base_query += " AND jqgjz LIKE '%涉黄%'"
            elif police_type == "涉赌警情":
                base_query += " AND jqgjz LIKE '%涉赌%'"
            elif police_type == "打架斗殴警情":
                base_query += " AND bjlbmc = '行政(治安)案件' AND bjlxmc = '打架斗殴'"
            elif police_type == "纠纷警情":
                base_query += " AND bjlbmc = '纠纷'"
            elif police_type == "求助警情":
                base_query += " AND bjlbmc = '求助'"

            # 如果提供了fkdwdm参数，添加筛选条件
            if fkdwdm is not None:
                base_query += " AND fkdwdm = :fkdwdm"
                params['fkdwdm'] = fkdwdm

            base_query += " ORDER BY bjsj"

            logger.info(f"查询{police_type}数据: {start_time} 到 {end_time}")

            # 执行查询
            results = db_manager.execute_query(base_query, params)

            if not results:
                logger.warning(f"未查询到{police_type}数据")
                return pd.DataFrame()

            # 转换为DataFrame
            df = pd.DataFrame(results)

            # 数据预处理
            df = self._preprocess_data(df)

            logger.info(f"成功获取 {len(df)} 条{police_type}数据")
            return df

        except Exception as e:
            logger.error(f"获取{police_type}数据失败: {e}")
            raise

    def get_police_overview_stats(self, start_time: str, end_time: str, fkdwdm: Optional[str] = None) -> Dict[str, int]:
        """获取总警情概况统计"""
        try:
            police_types = [
                "总警情", "刑事案件", "治安案件", "盗窃案件", "诈骗案件",
                "通讯网络诈骗案件", "故意损毁财物案件", "殴打他人案件",
                "打架斗殴警情", "涉黄警情", "涉赌警情", "纠纷警情", "求助警情"
            ]

            stats = {}

            for police_type in police_types:
                if police_type == "总警情":
                    df = self.get_police_data(start_time, end_time, fkdwdm)
                else:
                    df = self.get_specific_police_data(start_time, end_time, police_type, fkdwdm)

                stats[police_type] = len(df)
                logger.info(f"{police_type}: {len(df)}条")

            return stats

        except Exception as e:
            logger.error(f"获取警情概况统计失败: {e}")
            raise

    def get_police_overview_with_comparison(self, start_time: str, end_time: str, fkdwdm: Optional[str] = None) -> Dict[str, Any]:
        """获取总警情概况统计（包含同比环比）"""
        try:
            police_types = [
                "总警情", "刑事案件", "治安案件", "盗窃案件", "诈骗案件",
                "通讯网络诈骗案件", "故意损毁财物案件", "殴打他人案件",
                "打架斗殴警情", "涉黄警情", "涉赌警情", "纠纷警情", "求助警情"
            ]

            overview_data = {}

            for police_type in police_types:
                # 获取当前期间数据
                if police_type == "总警情":
                    current_df = self.get_police_data(start_time, end_time, fkdwdm)
                else:
                    current_df = self.get_specific_police_data(start_time, end_time, police_type, fkdwdm)

                current_count = len(current_df)

                # 获取环比数据
                period_comparison_df = self.get_comparison_data(start_time, end_time, "period", fkdwdm)
                if police_type != "总警情" and not period_comparison_df.empty:
                    period_comparison_df = self._filter_by_police_type(period_comparison_df, police_type)
                period_count = len(period_comparison_df) if not period_comparison_df.empty else 0

                # 获取同比数据
                year_comparison_df = self.get_comparison_data(start_time, end_time, "year", fkdwdm)
                if police_type != "总警情" and not year_comparison_df.empty:
                    year_comparison_df = self._filter_by_police_type(year_comparison_df, police_type)
                year_count = len(year_comparison_df) if not year_comparison_df.empty else 0

                # 计算变化率
                period_change = self._calculate_change_rate(current_count, period_count)
                year_change = self._calculate_change_rate(current_count, year_count)

                overview_data[police_type] = {
                    "current": current_count,
                    "period_comparison": period_count,
                    "year_comparison": year_count,
                    "period_change": period_change,
                    "year_change": year_change
                }

                logger.info(f"{police_type}: 当前{current_count}条, 环比{period_change['rate']:.1f}%, 同比{year_change['rate']:.1f}%")

            return overview_data

        except Exception as e:
            logger.error(f"获取警情概况统计失败: {e}")
            raise

    def _filter_by_police_type(self, df: pd.DataFrame, police_type: str) -> pd.DataFrame:
        """根据警情类型筛选数据（严格按照指定的查询条件）"""
        if police_type == "刑事案件":
            return df[df['police_situation_category'] == '刑事案件']
        elif police_type == "治安案件":
            return df[df['police_situation_category'] == '行政(治安)案件']
        elif police_type == "盗窃案件":
            return df[((df['police_situation_category'] == '刑事案件') | (df['police_situation_category'] == '行政(治安)案件')) &
                     ((df['police_situation_type'] == '盗窃') | (df['police_situation_type'] == '盗窃案'))]
        elif police_type == "诈骗案件":
            return df[((df['police_situation_category'] == '刑事案件') | (df['police_situation_category'] == '行政(治安)案件')) &
                     ((df['police_situation_type'] == '诈骗') | (df['police_situation_type'] == '诈骗案'))]
        elif police_type == "通讯网络诈骗案件":
            return df[((df['police_situation_category'] == '刑事案件') | (df['police_situation_category'] == '行政(治安)案件')) &
                     ((df['police_situation_type'] == '诈骗') | (df['police_situation_type'] == '诈骗案')) &
                     ((df['cause'] == '网络诈骗') | (df['cause'] == '电信诈骗'))]
        elif police_type == "故意损毁财物案件":
            return df[((df['police_situation_category'] == '刑事案件') | (df['police_situation_category'] == '行政(治安)案件')) &
                     (df['police_situation_type'] == '故意损毁财物')]
        elif police_type == "殴打他人案件":
            return df[((df['police_situation_category'] == '刑事案件') | (df['police_situation_category'] == '行政(治安)案件')) &
                     (df['police_situation_type'] == '殴打他人')]
        elif police_type == "涉黄警情":
            return df[df['jqgjz'].str.contains('涉黄', na=False)]
        elif police_type == "涉赌警情":
            return df[df['jqgjz'].str.contains('涉赌', na=False)]
        elif police_type == "打架斗殴警情":
            return df[(df['bjlbmc'] == '行政(治安)案件') & (df['bjlxmc'] == '打架斗殴')]
        elif police_type == "纠纷警情":
            return df[df['bjlbmc'] == '纠纷']
        elif police_type == "求助警情":
            return df[df['bjlbmc'] == '求助']
        else:
            return df

    def _calculate_change_rate(self, current: int, comparison: int) -> Dict[str, Any]:
        """计算变化率"""
        if comparison == 0:
            if current == 0:
                return {"rate": 0.0, "trend": "持平", "change": 0}
            else:
                return {"rate": 100.0, "trend": "新增", "change": current}

        rate = ((current - comparison) / comparison) * 100
        change = current - comparison

        if rate > 0:
            trend = "上升"
        elif rate < 0:
            trend = "下降"
        else:
            trend = "持平"

        return {
            "rate": round(rate, 1),
            "trend": trend,
            "change": change
        }

    def get_multi_period_analysis(self, start_time: str, end_time: str, fkdwdm: Optional[str] = None) -> Dict[str, Any]:
        """获取多期间对比分析"""
        try:
            # 获取当前期间数据
            current_df = self.get_police_data(start_time, end_time, fkdwdm)

            if current_df.empty:
                return {
                    "current_period": None,
                    "period_comparison": None,
                    "year_comparison": None,
                    "message": "当前期间无数据"
                }

            # 获取环比数据
            period_comparison_df = self.get_comparison_data(start_time, end_time, "period", fkdwdm)

            # 获取同比数据
            year_comparison_df = self.get_comparison_data(start_time, end_time, "year", fkdwdm)

            # 进行基础统计分析（避免循环导入）
            result = {
                "current_period": {
                    "data_count": len(current_df),
                    "time_range": f"{start_time} 至 {end_time}"
                },
                "period_comparison": None,
                "year_comparison": None
            }

            # 环比分析
            if not period_comparison_df.empty:
                analysis_engine = get_analysis_engine()
                period_analysis = analysis_engine.generate_comparison_analysis(
                    current_df, period_comparison_df, "period"
                )
                result["period_comparison"] = period_analysis

            # 同比分析
            if not year_comparison_df.empty:
                analysis_engine = get_analysis_engine()
                year_analysis = analysis_engine.generate_comparison_analysis(
                    current_df, year_comparison_df, "year"
                )
                result["year_comparison"] = year_analysis

            logger.info("多期间对比分析完成")
            return result

        except Exception as e:
            logger.error(f"多期间对比分析失败: {e}")
            raise

    def get_police_station_name(self, fkdwdm: str) -> str:
        """根据派出所代码获取派出所名称"""
        try:
            if not fkdwdm:
                return "全部派出所"

            # 从zzjg_zzjg表查询派出所名称
            query = """
            SELECT JGJC1
            FROM zzjg_zzjg
            WHERE JGDM = :fkdwdm
            LIMIT 1
            """

            result = db_manager.execute_query(query, {'fkdwdm': fkdwdm})

            if result and len(result) > 0:
                return result[0]['JGJC1']
            else:
                logger.warning(f"未找到派出所代码 {fkdwdm} 对应的名称")
                return f"派出所代码：{fkdwdm}"

        except Exception as e:
            logger.error(f"获取派出所名称失败: {e}")
            return f"派出所代码：{fkdwdm}"


# 全局数据服务实例
data_service = DataService()

