# 月报告系统Docker离线部署完整教程

## 概述
本教程将指导你在Windows联网机器上打包Docker镜像，然后在离线Ubuntu服务器上部署运行月报告生成系统。

系统支持三种运行模式：
1. **批量模式** - 一次性生成2025年1-6月历史报告
2. **定时任务模式** - 每月3号自动生成上个月报告
3. **完整模式** - Web服务 + 定时任务 + 初始批量生成（推荐）

## 前置条件

### Windows机器（联网）
- 安装Docker Desktop
- 确保Docker服务正在运行
- 有足够的磁盘空间（建议至少5GB）

### Ubuntu服务器（离线）
- 已安装Docker和Docker Compose
- 有足够的磁盘空间存储镜像和数据

## 第一步：在Windows机器上构建和打包

### 1.1 检查Docker环境
```cmd
docker --version
docker-compose --version
```

### 1.2 运行构建脚本
在项目根目录下运行：

**方式一：使用批处理文件（推荐）**
```cmd
build_docker.bat
```

**方式二：使用bash脚本（如果安装了Git Bash）**
```bash
chmod +x build_docker.sh
./build_docker.sh
```

### 1.3 构建过程说明
脚本会执行以下操作：
1. 创建输出目录 `docker_package`
2. 构建Docker镜像
3. 导出镜像为tar文件
4. 复制部署相关文件
5. 创建配置模板和说明文档

### 1.4 验证构建结果
构建完成后，检查 `docker_package` 目录：
```
docker_package/
├── monthly-reports_latest.tar    # Docker镜像文件（主要文件）
├── docker-compose.yml           # Docker Compose配置
├── deploy_docker.sh            # Ubuntu部署脚本
├── .env.template              # 环境变量模板
└── README_DEPLOY.md          # 部署说明
```

## 第二步：传输到Ubuntu服务器

### 2.1 打包传输
将整个 `docker_package` 目录压缩并传输到Ubuntu服务器：

```bash
# 在Windows上压缩（可选）
tar -czf docker_package.tar.gz docker_package/

# 或者直接传输整个目录
scp -r docker_package/ user@server:/path/to/destination/
```

### 2.2 在Ubuntu服务器上解压（如果压缩了）
```bash
tar -xzf docker_package.tar.gz
cd docker_package
```

## 第三步：在Ubuntu服务器上部署

### 3.1 检查Docker环境
```bash
sudo docker --version
sudo docker-compose --version
```

如果未安装，请先安装：
```bash
sudo apt update
sudo apt install docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker
```

### 3.2 运行部署脚本（推荐）
```bash
cd docker_package
chmod +x deploy_docker.sh
./deploy_docker.sh
```

部署脚本会提供以下选项：
1. **批量模式** - 一次性生成2025年1-6月历史报告
2. **定时任务模式** - 设置定时任务，每月3号自动生成报告
3. **完整模式** - Web服务 + 定时任务 + 初始批量生成（推荐）
4. **手动选择** - 显示所有可用命令

### 3.3 手动部署选项

#### 批量生成历史报告（一次性）
```bash
sudo docker load -i monthly-reports_latest.tar
mkdir -p output/{reports,charts,logs}
sudo docker-compose --profile batch up
```

#### 启动定时任务服务（持续运行）
```bash
sudo docker load -i monthly-reports_latest.tar
mkdir -p output/{reports,charts,logs}
sudo docker-compose --profile cron up -d
```

#### 启动完整服务（推荐）
```bash
sudo docker load -i monthly-reports_latest.tar
mkdir -p output/{reports,charts,logs}
sudo docker-compose --profile daemon up -d
```

## 第四步：验证和管理

### 4.1 检查服务状态
```bash
sudo docker-compose ps
```

### 4.2 查看日志
```bash
# 查看实时日志
sudo docker-compose logs -f

# 查看最近的日志
sudo docker-compose logs --tail=50
```

### 4.3 进入容器（调试用）
```bash
sudo docker-compose exec monthly-reports bash
```

### 4.4 重启服务
```bash
sudo docker-compose restart
```

### 4.5 停止服务
```bash
sudo docker-compose down
```

## 配置说明

### 环境变量配置
编辑 `docker-compose.yml` 中的环境变量部分，或创建 `.env` 文件：

```env
# 数据库配置
DB_HOST=*************
DB_PORT=3306
DB_USER=root
DB_PASSWORD=jkga@qzlq
DB_NAME=llm

# LLM服务配置
LLM_URL=http://************:8889
LLM_API_KEY=sk-HBE4QnJ910R7ntO1421c91CdA56a40589aB1B72f3524C460
LLM_MODEL=Qwen3-235B-A22B

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=1998
APP_DEBUG=false
```

### 数据持久化
输出文件会保存在宿主机的以下目录：
- `./output/reports/` - 生成的报告文件
- `./output/charts/` - 生成的图表文件
- `./output/logs/` - 应用日志文件

## 常见问题和解决方案

### Q1: Docker镜像构建失败
**解决方案：**
1. 检查Docker Desktop是否正在运行
2. 确保网络连接正常
3. 检查磁盘空间是否充足
4. 查看构建日志中的具体错误信息

### Q2: 镜像加载失败
**解决方案：**
1. 检查tar文件是否完整
2. 确保有足够的磁盘空间
3. 检查Docker服务是否正常运行

### Q3: 服务启动失败
**解决方案：**
1. 检查端口是否被占用：`sudo netstat -tlnp | grep 1998`
2. 检查环境变量配置是否正确
3. 查看详细日志：`sudo docker-compose logs`

### Q4: 无法连接数据库
**解决方案：**
1. 确认数据库服务器地址和端口
2. 检查网络连通性：`ping *************`
3. 验证数据库凭据

### Q5: 权限问题
**解决方案：**
```bash
# 修改输出目录权限
sudo chown -R $USER:$USER output/

# 或者使用sudo运行docker-compose
sudo docker-compose up -d
```

## 性能优化建议

1. **资源限制**：在docker-compose.yml中添加资源限制
2. **日志轮转**：配置日志轮转避免日志文件过大
3. **定期清理**：定期清理旧的报告文件和日志
4. **监控**：设置监控来跟踪应用性能

## 安全建议

1. **敏感信息**：不要在镜像中硬编码敏感信息
2. **网络安全**：限制容器的网络访问
3. **用户权限**：避免使用root用户运行容器
4. **定期更新**：定期更新基础镜像和依赖包

## 备份和恢复

### 备份
```bash
# 备份输出数据
tar -czf backup_$(date +%Y%m%d).tar.gz output/

# 备份配置
cp docker-compose.yml docker-compose.yml.backup
```

### 恢复
```bash
# 恢复数据
tar -xzf backup_YYYYMMDD.tar.gz

# 恢复配置
cp docker-compose.yml.backup docker-compose.yml
```

这个完整的教程应该能帮助你成功地在离线环境中部署Docker应用。如果遇到任何问题，请查看日志文件获取更多详细信息。
