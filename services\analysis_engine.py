"""
数据分析引擎
"""
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from loguru import logger
import numpy as np
from collections import defaultdict


class AnalysisEngine:
    """数据分析引擎"""
    
    def __init__(self):
        # 更新后的警情维度组合（与新的筛选规则保持一致）
        self.predefined_dimensions = {
            "街面违法犯罪": {
                "categories": ["刑事案件", "行政(治安)案件"],
                "types": ["聚众斗殴", "殴打他人", "故意伤害", "寻衅滋事", "盗窃", "盗窃案", "抢劫", "抢夺"]
            },
            "诈骗类案件": {
                "categories": ["刑事案件", "行政(治安)案件"],
                "types": ["诈骗", "诈骗案"]
            },
            "民生服务类": {
                "categories": ["求助"],
                "types": ["救助", "咨询", "投诉", "举报"]
            },
            "纠纷调解类": {
                "categories": ["纠纷"],
                "types": ["民事纠纷", "经济纠纷", "邻里纠纷", "家庭纠纷"]
            },
            "治安管理类": {
                "categories": ["行政(治安)案件"],
                "types": ["打架斗殴", "噪音扰民", "违法经营", "赌博", "卖淫嫖娼"]
            }
        }
    
    def comprehensive_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """综合分析"""
        try:
            logger.info("开始综合数据分析")
            
            analysis_result = {
                "basic_stats": self._basic_statistics(df),
                "time_analysis": self._time_analysis(df),
                "geographic_analysis": self._geographic_analysis(df),
                "category_analysis": self._category_analysis(df),
                # "response_analysis": self._response_analysis(df),  # 移除cjqk分析
                "dimension_analysis": self._dimension_analysis(df),
                "trend_analysis": self._trend_analysis(df),
                "hotspot_analysis": self._hotspot_analysis(df)
            }
            
            logger.info("综合数据分析完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"综合分析失败: {e}")
            raise
    
    def _basic_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """基础统计分析"""
        if df.empty:
            return {
                "total_records": 0,
                "date_range": {"start": "", "end": "", "days": 0},
                "daily_average": 0,
                "unique_counties": 0,
                "unique_communities": 0,
                "unique_categories": 0
            }

        # 计算实际天数：从开始日期到结束日期的完整天数
        start_date = df['bjsj'].min().date()
        end_date = df['bjsj'].max().date()
        days = (end_date - start_date).days + 1

        return {
            "total_records": len(df),
            "date_range": {
                "start": start_date.strftime('%Y-%m-%d'),
                "end": end_date.strftime('%Y-%m-%d'),
                "days": days
            },
            "daily_average": len(df) / days if days > 0 else 0,
            "unique_counties": df['county'].nunique(),
            "unique_communities": df['community'].nunique(),
            "unique_categories": df['police_situation_category'].nunique(),
            "unique_fkdwdm": df['fkdwdm'].nunique() if 'fkdwdm' in df.columns else 0
        }
    
    def _time_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """时间分析"""
        # 检查hour列是否存在
        if 'hour' not in df.columns:
            logger.warning("DataFrame中缺少hour列，无法进行时间分析")
            return {
                "hourly_distribution": {},
                "weekly_distribution": {},
                "daily_distribution": {},
                "peak_hours": [],
                "peak_hour_counts": []
            }

        # 小时分布
        hourly_dist = df.groupby('hour').size().to_dict()
        logger.info(f"原始小时分布数据: {hourly_dist}")

        # 星期分布
        weekly_dist = df.groupby('day_name').size().to_dict()

        # 日期分布（按天）
        daily_dist = df.groupby(df['bjsj'].dt.date).size().to_dict()
        daily_dist = {str(k): int(v) for k, v in daily_dist.items()}

        # 高峰时段分析
        peak_hours = sorted(hourly_dist.items(), key=lambda x: x[1], reverse=True)[:3]

        # 格式化小时分布数据
        formatted_hourly = {f"{h:02d}时": count for h, count in hourly_dist.items()}
        logger.info(f"格式化后小时分布数据: {formatted_hourly}")

        return {
            "hourly_distribution": formatted_hourly,
            "weekly_distribution": self._translate_weekdays(weekly_dist),
            "daily_distribution": daily_dist,
            "peak_hours": [f"{h:02d}时" for h, _ in peak_hours],
            "peak_hour_counts": [count for _, count in peak_hours]
        }
    
    def _geographic_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """地域分析"""
        # 区县分布
        county_dist = df['county'].value_counts().to_dict()

        # 社区分布（前20）
        community_dist = df['community'].value_counts().head(20).to_dict()

        # 派出所分布（前20）
        fkdwdm_dist = df['fkdwdm'].value_counts().head(20).to_dict() if 'fkdwdm' in df.columns else {}

        # 区县-社区组合分析
        county_community = df.groupby(['county', 'community']).size().reset_index(name='count')
        top_combinations = county_community.nlargest(10, 'count')

        return {
            "county_distribution": county_dist,
            "community_distribution": community_dist,
            "fkdwdm_distribution": fkdwdm_dist,
            "top_county_community": [
                {
                    "county": row['county'],
                    "community": row['community'],
                    "count": int(row['count'])
                }
                for _, row in top_combinations.iterrows()
            ],
            "county_ranking": list(county_dist.keys())[:10]
        }
    
    def _category_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """警情类别分析"""
        # 三级分类统计
        category_dist = df['police_situation_category'].value_counts().to_dict()
        type_dist = df['police_situation_type'].value_counts().head(20).to_dict()
        cause_dist = df['cause'].value_counts().head(20).to_dict()

        # 类别组合分析
        category_type = df.groupby(['police_situation_category', 'police_situation_type']).size()
        top_combinations = category_type.nlargest(15).reset_index(name='count')
        
        return {
            "category_distribution": category_dist,
            "type_distribution": type_dist,
            "cause_distribution": cause_dist,
            "category_type_combinations": [
                {
                    "category": row['police_situation_category'],
                    "type": row['police_situation_type'],
                    "count": int(row['count'])
                }
                for _, row in top_combinations.iterrows()
            ]
        }
    
    # 移除response_analysis方法，因为cjqk是反馈内容，不需要分析
    
    def _dimension_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """预定义维度分析"""
        dimension_results = {}
        
        for dim_name, dim_config in self.predefined_dimensions.items():
            # 筛选数据
            filtered_df = df[
                df['police_situation_category'].isin(dim_config['categories']) &
                df['police_situation_type'].isin(dim_config['types'])
            ]
            
            if len(filtered_df) > 0:
                dimension_results[dim_name] = {
                    "count": len(filtered_df),
                    "percentage": len(filtered_df) / len(df) * 100,
                    "county_distribution": filtered_df['county'].value_counts().head(10).to_dict(),
                    "fkdwdm_distribution": filtered_df['fkdwdm'].value_counts().head(10).to_dict() if 'fkdwdm' in filtered_df.columns else {},
                    "hourly_pattern": filtered_df.groupby('hour').size().to_dict(),
                    "top_types": filtered_df['police_situation_type'].value_counts().head(5).to_dict()
                }
        
        return dimension_results
    
    def _trend_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """趋势分析"""
        # 按日期统计
        daily_counts = df.groupby(df['bjsj'].dt.date).size()

        # 计算移动平均
        if len(daily_counts) >= 7:
            moving_avg_7 = daily_counts.rolling(window=7).mean()
            trend_data = {
                "daily_counts": {str(k): int(v) for k, v in daily_counts.items()},
                "moving_average_7": {str(k): float(v) for k, v in moving_avg_7.dropna().items()}
            }
        else:
            trend_data = {
                "daily_counts": {str(k): int(v) for k, v in daily_counts.items()},
                "moving_average_7": {}
            }

        return trend_data

    def generate_comparison_analysis(self, current_df: pd.DataFrame,
                                   comparison_df: pd.DataFrame = None,
                                   comparison_type: str = "period") -> Dict[str, Any]:
        """生成同比环比分析

        Args:
            current_df: 当前期间数据
            comparison_df: 对比期间数据（如果为None，则从数据库获取）
            comparison_type: 对比类型 ("period"=环比, "year"=同比)
        """
        try:
            logger.info(f"开始{comparison_type}分析")

            # 当前期间统计
            current_stats = self._calculate_period_stats(current_df)

            # 如果没有提供对比数据，返回基础统计
            if comparison_df is None or comparison_df.empty:
                return {
                    "comparison_type": comparison_type,
                    "current_period": current_stats,
                    "comparison_period": None,
                    "changes": None,
                    "analysis_text": "无对比期间数据，无法进行同比环比分析"
                }

            # 对比期间统计
            comparison_stats = self._calculate_period_stats(comparison_df)

            # 计算变化率
            changes = self._calculate_changes(current_stats, comparison_stats)

            # 生成分析文本
            analysis_text = self._generate_comparison_text(changes, comparison_type)

            result = {
                "comparison_type": comparison_type,
                "current_period": current_stats,
                "comparison_period": comparison_stats,
                "changes": changes,
                "analysis_text": analysis_text
            }

            logger.info(f"{comparison_type}分析完成")
            return result

        except Exception as e:
            logger.error(f"{comparison_type}分析失败: {e}")
            raise
    
    def _hotspot_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """热点分析"""
        # 时间热点
        time_hotspots = df.groupby(['hour', 'day_name']).size().reset_index(name='count')
        top_time_hotspots = time_hotspots.nlargest(10, 'count')
        
        # 地域热点
        geo_hotspots = df.groupby(['county', 'community']).size().reset_index(name='count')
        top_geo_hotspots = geo_hotspots.nlargest(10, 'count')
        
        return {
            "time_hotspots": [
                {
                    "hour": int(row['hour']),
                    "day": self._translate_weekday(row['day_name']),
                    "count": int(row['count'])
                }
                for _, row in top_time_hotspots.iterrows()
            ],
            "geographic_hotspots": [
                {
                    "county": row['county'],
                    "community": row['community'],
                    "count": int(row['count'])
                }
                for _, row in top_geo_hotspots.iterrows()
            ]
        }
    
    def _translate_weekdays(self, weekly_dist: Dict) -> Dict[str, int]:
        """翻译星期"""
        day_mapping = {
            'Monday': '周一', 'Tuesday': '周二', 'Wednesday': '周三',
            'Thursday': '周四', 'Friday': '周五', 'Saturday': '周六', 'Sunday': '周日'
        }
        return {day_mapping.get(k, k): v for k, v in weekly_dist.items()}
    
    def _translate_weekday(self, day_name: str) -> str:
        """翻译单个星期"""
        day_mapping = {
            'Monday': '周一', 'Tuesday': '周二', 'Wednesday': '周三',
            'Thursday': '周四', 'Friday': '周五', 'Saturday': '周六', 'Sunday': '周日'
        }
        return day_mapping.get(day_name, day_name)
    
    # 移除_calculate_response_rates方法，因为基于cjqk字段，不需要分析

    def _calculate_period_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算期间统计数据"""
        if df.empty:
            return {
                "total_count": 0,
                "daily_average": 0,
                "county_top5": {},
                "category_top5": {},
                "hourly_peak": {},
                # "response_rate": 0  # 移除出警率，基于cjqk字段
            }

        # 基础统计
        total_count = len(df)
        start_date = df['bjsj'].min().date()
        end_date = df['bjsj'].max().date()
        days = (end_date - start_date).days + 1
        daily_average = total_count / days if days > 0 else 0

        # 区县TOP5
        county_top5 = df['county'].value_counts().head(5).to_dict()

        # 类别TOP5
        category_top5 = df['police_situation_category'].value_counts().head(5).to_dict()

        # 派出所TOP5
        fkdwdm_top5 = df['fkdwdm'].value_counts().head(5).to_dict() if 'fkdwdm' in df.columns else {}

        # 小时分布峰值
        hourly_dist = df.groupby('hour').size()
        peak_hour = hourly_dist.idxmax() if not hourly_dist.empty else 0
        hourly_peak = {f"{peak_hour:02d}时": int(hourly_dist.max()) if not hourly_dist.empty else 0}

        # 移除出警率计算，基于cjqk字段

        return {
            "total_count": total_count,
            "daily_average": round(daily_average, 2),
            "county_top5": county_top5,
            "category_top5": category_top5,
            "fkdwdm_top5": fkdwdm_top5,
            "hourly_peak": hourly_peak,
            # "response_rate": round(response_rate, 2)  # 移除出警率
        }

    def _calculate_changes(self, current: Dict[str, Any], comparison: Dict[str, Any]) -> Dict[str, Any]:
        """计算变化率"""
        changes = {}

        # 总量变化
        current_total = current.get('total_count', 0)
        comparison_total = comparison.get('total_count', 0)

        if comparison_total > 0:
            total_change_rate = ((current_total - comparison_total) / comparison_total) * 100
            total_change_abs = current_total - comparison_total
        else:
            total_change_rate = 0
            total_change_abs = current_total

        changes['total'] = {
            'current': current_total,
            'comparison': comparison_total,
            'absolute_change': total_change_abs,
            'change_rate': round(total_change_rate, 2),
            'trend': '上升' if total_change_rate > 0 else '下降' if total_change_rate < 0 else '持平'
        }

        # 日均变化
        current_daily = current.get('daily_average', 0)
        comparison_daily = comparison.get('daily_average', 0)

        if comparison_daily > 0:
            daily_change_rate = ((current_daily - comparison_daily) / comparison_daily) * 100
        else:
            daily_change_rate = 0

        changes['daily_average'] = {
            'current': current_daily,
            'comparison': comparison_daily,
            'change_rate': round(daily_change_rate, 2),
            'trend': '上升' if daily_change_rate > 0 else '下降' if daily_change_rate < 0 else '持平'
        }

        # 移除出警率变化分析，基于cjqk字段

        # 区县分布变化
        changes['county_changes'] = self._calculate_ranking_changes(
            current.get('county_top5', {}),
            comparison.get('county_top5', {})
        )

        # 类别分布变化
        changes['category_changes'] = self._calculate_ranking_changes(
            current.get('category_top5', {}),
            comparison.get('category_top5', {})
        )

        return changes

    def _calculate_ranking_changes(self, current_dict: Dict[str, int],
                                 comparison_dict: Dict[str, int]) -> List[Dict[str, Any]]:
        """计算排名变化"""
        changes = []

        current_ranking = list(current_dict.keys())
        comparison_ranking = list(comparison_dict.keys())

        for i, item in enumerate(current_ranking[:5]):
            current_rank = i + 1
            current_count = current_dict.get(item, 0)

            # 查找在对比期间的排名
            if item in comparison_ranking:
                comparison_rank = comparison_ranking.index(item) + 1
                comparison_count = comparison_dict.get(item, 0)
                rank_change = comparison_rank - current_rank

                if comparison_count > 0:
                    count_change_rate = ((current_count - comparison_count) / comparison_count) * 100
                else:
                    count_change_rate = 0
            else:
                comparison_rank = None
                comparison_count = 0
                rank_change = None
                count_change_rate = 0

            changes.append({
                'item': item,
                'current_rank': current_rank,
                'current_count': current_count,
                'comparison_rank': comparison_rank,
                'comparison_count': comparison_count,
                'rank_change': rank_change,
                'count_change_rate': round(count_change_rate, 2),
                'rank_trend': '上升' if rank_change and rank_change > 0 else '下降' if rank_change and rank_change < 0 else '持平' if rank_change == 0 else '新进'
            })

        return changes

    def _generate_comparison_text(self, changes: Dict[str, Any], comparison_type: str) -> str:
        """生成对比分析文本"""
        period_name = "同期" if comparison_type == "year" else "上期"

        total_change = changes.get('total', {})
        daily_change = changes.get('daily_average', {})
        response_change = changes.get('response_rate', {})

        text_parts = []

        # 总量变化
        total_trend = total_change.get('trend', '持平')
        total_rate = abs(total_change.get('change_rate', 0))
        text_parts.append(f"与{period_name}相比，警情总量{total_trend}{total_rate:.1f}%")

        # 日均变化
        daily_trend = daily_change.get('trend', '持平')
        daily_rate = abs(daily_change.get('change_rate', 0))
        if daily_rate > 0:
            text_parts.append(f"日均警情{daily_trend}{daily_rate:.1f}%")

        # 移除出警率变化文本，基于cjqk字段

        return "，".join(text_parts) + "。"

    def comprehensive_special_analysis(self, data_service, start_time: str, end_time: str,
                                     fkdwdm: Optional[str] = None) -> Dict[str, Any]:
        """综合专项分析"""
        try:
            logger.info("开始综合专项分析")

            special_analysis = {}

            # 1. 刑事案件分析
            criminal_df = data_service.get_specific_police_data(start_time, end_time, "刑事案件", fkdwdm)
            if not criminal_df.empty:
                special_analysis["criminal_analysis"] = self._analyze_criminal_cases(criminal_df)

            # 2. 治安案件分析
            security_df = data_service.get_specific_police_data(start_time, end_time, "治安案件", fkdwdm)
            if not security_df.empty:
                special_analysis["security_analysis"] = self._analyze_security_cases(security_df)

            # 3. 盗窃案件分析
            theft_df = data_service.get_specific_police_data(start_time, end_time, "盗窃案件", fkdwdm)
            if not theft_df.empty:
                special_analysis["theft_analysis"] = self._analyze_theft_cases(theft_df)

            # 4. 涉黄警情分析
            prostitution_df = data_service.get_specific_police_data(start_time, end_time, "涉黄警情", fkdwdm)
            if not prostitution_df.empty:
                special_analysis["prostitution_analysis"] = self._analyze_prostitution_cases(prostitution_df)

            # 5. 涉赌警情分析
            gambling_df = data_service.get_specific_police_data(start_time, end_time, "涉赌警情", fkdwdm)
            if not gambling_df.empty:
                special_analysis["gambling_analysis"] = self._analyze_gambling_cases(gambling_df)

            # 6. 打架斗殴警情分析
            fight_df = data_service.get_specific_police_data(start_time, end_time, "打架斗殴警情", fkdwdm)
            if not fight_df.empty:
                special_analysis["fight_analysis"] = self._analyze_fight_cases(fight_df)

            logger.info("综合专项分析完成")
            return special_analysis

        except Exception as e:
            logger.error(f"综合专项分析失败: {e}")
            return {}

    def _analyze_criminal_cases(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析刑事案件"""
        return {
            "total_count": len(df),
            "time_distribution": self._get_time_distribution(df),
            "location_distribution": self._get_location_distribution(df),
            "type_distribution": df['police_situation_type'].value_counts().head(10).to_dict(),
            "peak_hours": self._get_peak_hours(df)
        }

    def _analyze_security_cases(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析治安案件"""
        return {
            "total_count": len(df),
            "time_distribution": self._get_time_distribution(df),
            "location_distribution": self._get_location_distribution(df),
            "type_distribution": df['police_situation_type'].value_counts().head(10).to_dict(),
            "peak_hours": self._get_peak_hours(df)
        }

    def _analyze_theft_cases(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析盗窃案件"""
        return {
            "total_count": len(df),
            "time_distribution": self._get_time_distribution(df),
            "location_distribution": self._get_location_distribution(df),
            "type_distribution": df['police_situation_type'].value_counts().head(10).to_dict(),
            "peak_hours": self._get_peak_hours(df),
            "community_distribution": df['community'].value_counts().head(10).to_dict()
        }

    def _analyze_prostitution_cases(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析涉黄警情"""
        return {
            "total_count": len(df),
            "time_distribution": self._get_time_distribution(df),
            "location_distribution": self._get_location_distribution(df),
            "peak_hours": self._get_peak_hours(df),
            "keyword_analysis": self._analyze_keywords(df, 'jqgjz')
        }

    def _analyze_gambling_cases(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析涉赌警情"""
        return {
            "total_count": len(df),
            "time_distribution": self._get_time_distribution(df),
            "location_distribution": self._get_location_distribution(df),
            "peak_hours": self._get_peak_hours(df),
            "keyword_analysis": self._analyze_keywords(df, 'jqgjz')
        }

    def _analyze_fight_cases(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析打架斗殴警情"""
        return {
            "total_count": len(df),
            "time_distribution": self._get_time_distribution(df),
            "location_distribution": self._get_location_distribution(df),
            "peak_hours": self._get_peak_hours(df),
            "community_distribution": df['community'].value_counts().head(10).to_dict()
        }

    def _get_time_distribution(self, df: pd.DataFrame) -> Dict[str, int]:
        """获取时间分布"""
        return df.groupby('hour').size().to_dict()

    def _get_location_distribution(self, df: pd.DataFrame) -> Dict[str, int]:
        """获取地点分布"""
        return df['county'].value_counts().head(10).to_dict()

    def _get_peak_hours(self, df: pd.DataFrame) -> List[int]:
        """获取高峰时段"""
        hourly_dist = df.groupby('hour').size()
        return hourly_dist.nlargest(3).index.tolist()

    def _analyze_keywords(self, df: pd.DataFrame, column: str) -> Dict[str, int]:
        """分析关键词"""
        if column not in df.columns:
            return {}

        keywords_count = {}
        for text in df[column].dropna():
            # 简单的关键词统计
            if '涉黄' in str(text):
                keywords_count['涉黄'] = keywords_count.get('涉黄', 0) + 1
            if '涉赌' in str(text):
                keywords_count['涉赌'] = keywords_count.get('涉赌', 0) + 1

        return keywords_count


# 全局分析引擎实例
analysis_engine = AnalysisEngine()
