@echo off
REM Windows Docker build script for monthly reports system

setlocal enabledelayedexpansion

REM Configuration variables
set IMAGE_NAME=monthly-reports
set IMAGE_TAG=latest
set FULL_IMAGE_NAME=%IMAGE_NAME%:%IMAGE_TAG%
set OUTPUT_DIR=.\%IMAGE_NAME%
set TAR_FILE=%OUTPUT_DIR%\%IMAGE_NAME%_%IMAGE_TAG%.tar

echo ==========================================
echo Monthly Reports Docker Build Script (Windows)
echo ==========================================

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not installed or not running
    echo Please install Docker Desktop and make sure it is running
    pause
    exit /b 1
)

REM Create output directory
echo Creating output directory...
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM Clean old images (optional)
echo Cleaning old images...
docker rmi "%FULL_IMAGE_NAME%" 2>nul
if errorlevel 1 (
    echo No old image found, continuing...
) else (
    echo Old image removed
)

REM Build Docker image
echo Building Docker image...
echo Image name: %FULL_IMAGE_NAME%
docker build -t "%FULL_IMAGE_NAME%" .

if errorlevel 1 (
    echo ERROR: Docker image build failed
    pause
    exit /b 1
) else (
    echo SUCCESS: Docker image built successfully
)

REM Export image to tar file
echo Exporting image to tar file...
echo Output file: %TAR_FILE%
docker save -o "%TAR_FILE%" "%FULL_IMAGE_NAME%"

if errorlevel 1 (
    echo ERROR: Image export failed
    pause
    exit /b 1
) else (
    echo SUCCESS: Image exported successfully
)

REM Copy deployment files
echo Copying deployment files...
copy docker-compose.yml "%OUTPUT_DIR%\" >nul
copy deploy_docker.sh "%OUTPUT_DIR%\" >nul
copy docker_entrypoint.sh "%OUTPUT_DIR%\" >nul

REM Create environment template file
echo Creating environment template...
(
echo # Database configuration
echo DB_HOST=*************
echo DB_PORT=3306
echo DB_USER=root
echo DB_PASSWORD=jkga@qzlq
echo DB_NAME=llm
echo.
echo # LLM service configuration
echo LLM_URL=http://************:8889
echo LLM_API_KEY=sk-HBE4QnJ910R7ntO1421c91CdA56a40589aB1B72f3524C460
echo LLM_MODEL=Qwen3-235B-A22B
echo.
echo # Application configuration
echo APP_HOST=0.0.0.0
echo APP_PORT=1998
echo APP_DEBUG=false
) > "%OUTPUT_DIR%\.env.template"

REM Display summary information
echo.
echo ==========================================
echo Build completed successfully!
echo ==========================================
echo Output directory: %OUTPUT_DIR%
echo Image file: %TAR_FILE%

REM Display file size
for %%A in ("%TAR_FILE%") do echo File size: %%~zA bytes

echo.
echo Files included:
dir "%OUTPUT_DIR%" /b

echo.
echo Deployment instructions:
echo 1. Upload the entire %OUTPUT_DIR% directory to Ubuntu server
echo 2. Run ./deploy_docker.sh and select deployment mode
echo 3. Recommend selecting 'Complete Mode' for batch + cron
echo ==========================================

pause
