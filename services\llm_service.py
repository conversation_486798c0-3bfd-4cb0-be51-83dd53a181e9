"""
LLM服务模块
"""
import asyncio
import httpx
import json
import pandas as pd
import re
from typing import Dict, Any, List, Optional
from loguru import logger
from config import settings


class LLMService:
    """LLM服务类"""
    
    def __init__(self):
        self.base_url = settings.llm_url
        self.api_key = settings.llm_api_key
        self.model = settings.llm_model
        self.timeout = 300  # 5分钟超时，适应复杂分析任务
        self.max_context_tokens = 6000  # 保守估计，为其他内容留出空间

    def _estimate_tokens(self, text: str) -> int:
        """估算文本的token数量（粗略估计：中文1字符≈1token，英文1词≈1token）"""
        if not text:
            return 0
        # 简单估算：中文字符数 + 英文单词数
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_words = len(re.findall(r'[a-zA-Z]+', text))
        return chinese_chars + english_words

    def _get_smart_samples(self, df: pd.DataFrame, column: str = 'cjqk',
                          max_tokens: int = None, min_samples: int = 5) -> List[str]:
        """智能采样：分层采样 + 动态调整 + token控制"""
        if df.empty:
            return []

        if max_tokens is None:
            max_tokens = self.max_context_tokens

        # 获取有效案例
        valid_cases = df[column].dropna()
        if valid_cases.empty:
            return []

        total_cases = len(valid_cases)
        logger.info(f"开始智能采样，总案例数: {total_cases}")

        # 如果案例数很少，直接返回全部
        if total_cases <= min_samples:
            samples = valid_cases.tolist()
            total_tokens = sum(self._estimate_tokens(case) for case in samples)
            logger.info(f"案例数较少，返回全部 {len(samples)} 条，预估tokens: {total_tokens}")
            return samples

        # 分层采样策略
        samples = []
        current_tokens = 0

        try:
            # 尝试按时间分层（如果有时间字段）
            if 'bjsj' in df.columns:
                df_copy = df.copy()
                df_copy['hour'] = pd.to_datetime(df_copy['bjsj'], errors='coerce').dt.hour
                time_groups = df_copy.groupby('hour')[column].apply(list).to_dict()

                # 计算每个时间段的采样数
                num_groups = len(time_groups)
                if num_groups > 0:
                    samples_per_group = max(1, min_samples // num_groups)

                    for hour, group_cases in time_groups.items():
                        group_samples = 0
                        for case in group_cases:
                            if pd.isna(case) or len(str(case).strip()) < 10:
                                continue

                            case_tokens = self._estimate_tokens(str(case))
                            if current_tokens + case_tokens <= max_tokens and group_samples < samples_per_group:
                                samples.append(str(case))
                                current_tokens += case_tokens
                                group_samples += 1

                            if current_tokens >= max_tokens * 0.9:  # 留10%缓冲
                                break

                        if current_tokens >= max_tokens * 0.9:
                            break

            # 如果分层采样不够，补充随机采样
            if len(samples) < min_samples and current_tokens < max_tokens * 0.8:
                remaining_cases = [case for case in valid_cases if str(case) not in samples]

                for case in remaining_cases:
                    if pd.isna(case) or len(str(case).strip()) < 10:
                        continue

                    case_tokens = self._estimate_tokens(str(case))
                    if current_tokens + case_tokens <= max_tokens:
                        samples.append(str(case))
                        current_tokens += case_tokens

                        if len(samples) >= min_samples * 3:  # 最多3倍最小样本数
                            break
                    else:
                        break

        except Exception as e:
            logger.warning(f"分层采样失败，使用简单采样: {e}")
            # 降级到简单采样
            samples = []
            current_tokens = 0

            for case in valid_cases:
                if pd.isna(case) or len(str(case).strip()) < 10:
                    continue

                case_tokens = self._estimate_tokens(str(case))
                if current_tokens + case_tokens <= max_tokens:
                    samples.append(str(case))
                    current_tokens += case_tokens
                else:
                    break

        logger.info(f"智能采样完成，采样数: {len(samples)}/{total_cases}，预估tokens: {current_tokens}")
        return samples

    async def _call_llm(self, prompt: str, max_retries: int = 2) -> str:
        """调用LLM API，带重试机制"""
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    logger.info(f"LLM API调用重试 {attempt}/{max_retries}")

                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }

                payload = {
                    "model": self.model,
                    "messages": [
                        {
                            "role": "system",
                            "content": "你是一名专业的公安数据分析师，擅长分析警情数据并生成专业报告。请基于提供的真实数据进行分析，不要编造任何数据。"
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "temperature": 0.3,
                    "max_tokens": 2000
                }

                # 根据重试次数调整超时时间
                timeout = self.timeout + (attempt * 60)  # 每次重试增加1分钟

                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.post(
                        f"{self.base_url}/v1/chat/completions",
                        headers=headers,
                        json=payload
                    )

                    if response.status_code == 200:
                        result = response.json()
                        content = result["choices"][0]["message"]["content"]

                        # 清理响应内容，移除思考标签
                        content = self._clean_llm_response(content)
                        return content
                    else:
                        logger.error(f"LLM API调用失败: {response.status_code} - {response.text}")
                        raise Exception(f"LLM API调用失败: {response.status_code}")

            except (httpx.ReadTimeout, httpx.ConnectTimeout) as e:
                last_exception = e
                logger.warning(f"LLM API调用超时 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    await asyncio.sleep(5)  # 等待5秒后重试
                    continue
                else:
                    break
            except Exception as e:
                logger.error(f"LLM API调用异常: {e}")
                logger.error(f"异常类型: {type(e).__name__}")
                logger.error(f"异常详情: {str(e)}")
                raise

        # 如果所有重试都失败了
        logger.error(f"LLM API调用最终失败，已重试 {max_retries} 次")
        raise last_exception

    def _clean_llm_response(self, content: str) -> str:
        """清理LLM响应内容"""
        try:
            import re

            # 移除 <think>...</think> 标签及其内容
            content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)

            # 移除思考过程相关的内容
            content = re.sub(r'让我.*?分析', '', content, flags=re.DOTALL)
            content = re.sub(r'我需要.*?分析', '', content, flags=re.DOTALL)
            content = re.sub(r'首先.*?然后', '', content, flags=re.DOTALL)

            # 移除其他可能的标签
            content = re.sub(r'<[^>]+>', '', content)

            # 移除异常截断的内容（如果内容以不完整的句子结尾）
            lines = content.split('\n')
            cleaned_lines = []
            for line in lines:
                line = line.strip()
                if line and not line.endswith('...') and len(line) > 10:
                    cleaned_lines.append(line)

            content = '\n'.join(cleaned_lines)

            # 清理多余的空白字符
            content = re.sub(r'\n\s*\n', '\n\n', content)
            content = content.strip()

            return content

        except Exception as e:
            logger.warning(f"清理LLM响应内容时出错: {e}")
            return content

    def _extract_json_from_response(self, response: str) -> dict:
        """从LLM响应中提取JSON，使用更健壮的解析方法"""
        import json
        import re

        try:
            # 清理响应内容
            cleaned_response = self._clean_llm_response(response)

            # 方法1: 尝试直接解析整个响应
            try:
                return json.loads(cleaned_response)
            except:
                pass

            # 方法2: 查找JSON代码块（```json ... ```）
            json_block_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', cleaned_response, re.DOTALL)
            if json_block_match:
                try:
                    return json.loads(json_block_match.group(1))
                except:
                    pass

            # 方法3: 查找第一个完整的JSON对象
            json_match = re.search(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', cleaned_response, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except:
                    pass

            # 方法4: 查找从第一个{到最后一个}的内容
            json_start = cleaned_response.find('{')
            json_end = cleaned_response.rfind('}') + 1
            if json_start != -1 and json_end != -1 and json_start < json_end:
                json_str = cleaned_response[json_start:json_end]
                try:
                    return json.loads(json_str)
                except:
                    pass

            # 方法5: 尝试修复截断的JSON
            if json_start != -1:
                json_str = cleaned_response[json_start:]
                # 尝试修复截断的JSON
                if not json_str.endswith('}'):
                    # 查找最后一个完整的字段
                    if '"summary"' in json_str:
                        # 找到summary字段的开始位置
                        summary_start = json_str.rfind('"summary"')
                        if summary_start != -1:
                            # 截取到summary字段之前的内容
                            before_summary = json_str[:summary_start]
                            # 尝试添加一个简单的summary并闭合JSON
                            fixed_json = before_summary + '"summary": "分析总结"}'
                            try:
                                return json.loads(fixed_json)
                            except:
                                pass

                    # 如果找不到summary，尝试直接闭合
                    if json_str.endswith(',') or json_str.endswith('"'):
                        # 移除末尾的逗号或引号，然后闭合
                        json_str = json_str.rstrip(',"') + '}'
                        try:
                            return json.loads(json_str)
                        except:
                            pass

            # 方法6: 尝试修复常见的JSON格式问题
            # 移除JSON前后的说明文字
            lines = cleaned_response.split('\n')
            json_lines = []
            in_json = False

            for line in lines:
                line = line.strip()
                if line.startswith('{') or in_json:
                    in_json = True
                    json_lines.append(line)
                    if line.endswith('}') and line.count('{') <= line.count('}'):
                        break

            if json_lines:
                json_str = '\n'.join(json_lines)
                try:
                    return json.loads(json_str)
                except:
                    pass

            # 方法7: 尝试从文本中提取关键信息构造JSON
            # 针对单条分析的特殊处理
            if 'item' in cleaned_response.lower() or 'location' in cleaned_response.lower():
                try:
                    # 尝试提取物品和场所信息
                    result = {}

                    # 查找物品信息
                    for item in ['现金', '手机', '电动车', '电瓶', '自行车', '包包', '首饰', '电脑']:
                        if item in cleaned_response:
                            result['item'] = item
                            break
                    else:
                        result['item'] = '其他'

                    # 查找场所信息
                    for location in ['居民住宅', '商铺', '路边', '停车场', '小区', '办公楼', '餐饮场所', '娱乐场所', '街面道路', '居住区域', '足浴按摩类', '酒店宾馆类', '棋牌娱乐类', '出租屋类']:
                        if location in cleaned_response:
                            result['location'] = location
                            break
                    else:
                        result['location'] = '其他'

                    if result:
                        return result
                except:
                    pass

            # 如果所有方法都失败，抛出异常
            raise ValueError("无法从响应中提取有效的JSON格式")

        except Exception as e:
            logger.warning(f"JSON提取失败: {e}")
            raise
    




    async def analyze_criminal_security_deep(self, criminal_df, security_df) -> str:
        """刑事案件与治安案件深度分析 - 基于警情分类字段全量统计"""
        try:
            analysis_parts = []

            # 1. 刑事案件分析
            if not criminal_df.empty:
                # 统计警情类别 (一级)
                category_stats = criminal_df['police_situation_category'].value_counts().to_dict()
                # 统计警情类型 (二级)
                type_stats = criminal_df['police_situation_type'].value_counts().to_dict()
                # 统计警情小类/原因 (三级)
                cause_stats = criminal_df['cause'].value_counts().to_dict()

                criminal_analysis = f"""
刑事案件分析（共{len(criminal_df)}起）：

一级分类统计：
{chr(10).join([f"- {k}: {v}起" for k, v in list(category_stats.items())[:10]])}

二级分类统计：
{chr(10).join([f"- {k}: {v}起" for k, v in list(type_stats.items())[:15]])}

三级分类统计：
{chr(10).join([f"- {k}: {v}起" for k, v in list(cause_stats.items())[:20]])}
"""
                analysis_parts.append(criminal_analysis)

            # 2. 治安案件分析
            if not security_df.empty:
                # 统计警情类别 (一级)
                category_stats = security_df['police_situation_category'].value_counts().to_dict()
                # 统计警情类型 (二级)
                type_stats = security_df['police_situation_type'].value_counts().to_dict()
                # 统计警情小类/原因 (三级)
                cause_stats = security_df['cause'].value_counts().to_dict()

                security_analysis = f"""
治安案件分析（共{len(security_df)}起）：

一级分类统计：
{chr(10).join([f"- {k}: {v}起" for k, v in list(category_stats.items())[:10]])}

二级分类统计：
{chr(10).join([f"- {k}: {v}起" for k, v in list(type_stats.items())[:15]])}

三级分类统计：
{chr(10).join([f"- {k}: {v}起" for k, v in list(cause_stats.items())[:20]])}
"""
                analysis_parts.append(security_analysis)

            if not analysis_parts:
                return "暂无刑事案件和治安案件数据可供分析。"

            # 3. 生成综合分析
            stats_content = "\n".join(analysis_parts)

            prompt = f"""
基于以下刑事案件和治安案件的分类统计数据，进行专业分析：

{stats_content}

请从以下角度进行分析：
1. 案件类型分布特征和规律
2. 刑事案件与治安案件的结构对比
3. 高发案件类型的特点分析
4. 案件分布的趋势和规律总结
5. 对公安工作的指导意义

要求：
- 基于统计数据进行客观分析
- 提供专业的公安数据分析视角
- 重点关注高发案件类型和分布规律
- 连贯文字形式输出，内容简明扼要
- 控制在300-400字，不要在内容中显示字数
"""

            response = await self._call_llm(prompt)
            logger.info("刑事治安案件深度分析完成")
            return response

        except Exception as e:
            logger.error(f"刑事治安案件深度分析失败: {e}")
            return "刑事治安案件分析过程中出现异常，请检查数据质量。"

    async def analyze_criminal_cases_separate(self, criminal_df) -> str:
        """刑事案件单独分析 - 基于警情分类字段全量统计"""
        try:
            if criminal_df.empty:
                return "暂无刑事案件数据可供分析。"

            # 统计警情类别 (一级)
            category_stats = criminal_df['police_situation_category'].value_counts().to_dict()
            # 统计警情类型 (二级)
            type_stats = criminal_df['police_situation_type'].value_counts().to_dict()
            # 统计警情小类/原因 (三级)
            cause_stats = criminal_df['cause'].value_counts().to_dict()

            # 构建统计内容
            stats_content = f"""
刑事案件分类统计分析（总计{len(criminal_df)}起）

一级分类（警情类别）统计：
{chr(10).join([f"- {k}: {v}起 ({v/len(criminal_df)*100:.1f}%)" for k, v in category_stats.items()])}

二级分类（警情类型）统计：
{chr(10).join([f"- {k}: {v}起 ({v/len(criminal_df)*100:.1f}%)" for k, v in list(type_stats.items())[:20]])}

三级分类（警情小类/原因）统计：
{chr(10).join([f"- {k}: {v}起 ({v/len(criminal_df)*100:.1f}%)" for k, v in list(cause_stats.items())[:25]])}
"""

            prompt = f"""
基于以下刑事案件的分类统计数据，进行专业分析：

{stats_content}

请从以下角度进行分析：
1. 刑事案件类型分布特征
2. 高发刑事案件类型分析
3. 案件结构和规律总结
4. 对刑事案件防控的指导意义

要求：
- 基于统计数据进行客观分析
- 专业的刑事案件分析视角
- 重点关注高发案件类型和分布规律
- 连贯文字形式输出，内容简明扼要
- 控制在250-350字，不要在内容中显示字数
"""

            response = await self._call_llm(prompt)
            logger.info("刑事案件单独分析完成")
            return response

        except Exception as e:
            logger.error(f"刑事案件单独分析失败: {e}")
            return "刑事案件分析过程中出现异常，请检查数据质量。"

    async def analyze_security_cases_separate(self, security_df) -> str:
        """治安案件单独分析 - 基于警情分类字段全量统计"""
        try:
            if security_df.empty:
                return "暂无治安案件数据可供分析。"

            # 统计警情类别 (一级)
            category_stats = security_df['police_situation_category'].value_counts().to_dict()
            # 统计警情类型 (二级)
            type_stats = security_df['police_situation_type'].value_counts().to_dict()
            # 统计警情小类/原因 (三级)
            cause_stats = security_df['cause'].value_counts().to_dict()

            # 构建统计内容
            stats_content = f"""
治安案件分类统计分析（总计{len(security_df)}起）

一级分类（警情类别）统计：
{chr(10).join([f"- {k}: {v}起 ({v/len(security_df)*100:.1f}%)" for k, v in category_stats.items()])}

二级分类（警情类型）统计：
{chr(10).join([f"- {k}: {v}起 ({v/len(security_df)*100:.1f}%)" for k, v in list(type_stats.items())[:20]])}

三级分类（警情小类/原因）统计：
{chr(10).join([f"- {k}: {v}起 ({v/len(security_df)*100:.1f}%)" for k, v in list(cause_stats.items())[:25]])}
"""

            prompt = f"""
基于以下治安案件的分类统计数据，进行专业分析：

{stats_content}

请从以下角度进行分析：
1. 治安案件类型分布特征
2. 高发治安案件类型分析
3. 案件结构和规律总结
4. 对治安管理工作的指导意义

要求：
- 基于统计数据进行客观分析
- 专业的治安管理分析视角
- 重点关注高发案件类型和分布规律
- 连贯文字形式输出，内容简明扼要
- 控制在250-350字，不要在内容中显示字数
"""

            response = await self._call_llm(prompt)
            logger.info("治安案件单独分析完成")
            return response

        except Exception as e:
            logger.error(f"治安案件单独分析失败: {e}")
            return "治安案件分析过程中出现异常，请检查数据质量。"

    async def analyze_theft_clustering(self, theft_df) -> Dict[str, Any]:
        """盗窃案件聚类分析 - 单条分析后汇总"""
        try:
            if theft_df.empty:
                return {"items": {}, "locations": {}}

            # 初始化统计字典
            items_count = {}
            locations_count = {}

            # 分析全量数据，不做数量限制
            processed_count = 0
            success_count = 0

            logger.info(f"开始分析盗窃案件，总数：{len(theft_df)}，将分析全量数据")

            # 单条分析每个案件
            for index, (_, row) in enumerate(theft_df.iterrows()):
                cjqk = str(row.get('cjqk', '')).strip()
                jqxz = str(row.get('jqxz', '')).strip()

                # 过滤空数据和过短数据
                if len(cjqk) < 10:
                    continue

                processed_count += 1

                # 单条分析提示词 - 极简格式
                prompt = f"""
案件描述：{cjqk[:100]}

从以下选项中选择：
物品：现金、手机、电动车、电瓶、自行车、包包、首饰、电脑、其他
场所：居民住宅、商铺、路边、停车场、小区、办公楼、其他

只返回JSON格式：{{"item": "物品", "location": "场所"}}
"""

                try:
                    response = await self._call_llm(prompt)
                    result = self._extract_json_from_response(response)

                    if isinstance(result, dict) and 'item' in result and 'location' in result:
                        # 统计物品类型
                        item = result.get('item', '其他')
                        items_count[item] = items_count.get(item, 0) + 1

                        # 统计场所类型
                        location = result.get('location', '其他')
                        locations_count[location] = locations_count.get(location, 0) + 1

                        success_count += 1

                        if processed_count % 50 == 0:
                            logger.info(f"已处理 {processed_count}/{len(theft_df)} 条案件，成功 {success_count} 条")
                    else:
                        logger.warning(f"案件 {processed_count} JSON格式不正确: {result}")
                        # 分析失败的归入"其他"类别
                        items_count['其他'] = items_count.get('其他', 0) + 1
                        locations_count['其他'] = locations_count.get('其他', 0) + 1

                except Exception as e:
                    logger.warning(f"案件 {processed_count} 分析失败: {e}")
                    # 分析失败的归入"其他"类别
                    items_count['其他'] = items_count.get('其他', 0) + 1
                    locations_count['其他'] = locations_count.get('其他', 0) + 1

            logger.info(f"盗窃案件聚类分析完成，处理 {processed_count} 条，成功 {success_count} 条")
            logger.info(f"物品统计: {items_count}")
            logger.info(f"场所统计: {locations_count}")

            return {
                "items": items_count,
                "locations": locations_count
            }

        except Exception as e:
            logger.error(f"盗窃案件聚类分析失败: {e}")
            return {"items": {}, "locations": {}}

    async def analyze_theft_deep(self, theft_df, clustering_result) -> str:
        """盗窃案件分析"""
        try:
            if theft_df.empty:
                return "暂无盗窃案件数据可供分析。"

            # 获取时间分布
            hourly_dist = theft_df.groupby('hour').size().to_dict()
            peak_hours = sorted(hourly_dist.items(), key=lambda x: x[1], reverse=True)[:3]

            # 获取案例样本 - 使用智能采样
            case_samples = self._get_smart_samples(theft_df, 'cjqk', max_tokens=3000, min_samples=10)

            # 处理聚类结果
            if isinstance(clustering_result, dict):
                # 如果是字典格式，提取相关信息
                items_info = clustering_result.get('items', {})
                locations_info = clustering_result.get('locations', {})
                summary_info = clustering_result.get('summary', '')

                clustering_text = f"""
物品类型分布：{items_info}
场所类型分布：{locations_info}
聚类分析总结：{summary_info}
"""
            else:
                # 如果是字符串格式，直接使用
                clustering_text = str(clustering_result)

            prompt = f"""
基于以下盗窃案件数据进行分析：

案件总数：{len(theft_df)}起

聚类分析结果：
{clustering_text}

时间特征：
- 高发时段：{[f"{h}时({c}起)" for h, c in peak_hours]}

案例样本：
{chr(10).join(case_samples)}

请结合聚类结果和案例详情，从以下角度进行分析：
1. 高发场所特征分析
2. 高发物品特征分析
3. 作案时间特点分析
4. 常见作案手段和方式
5. 案件特征和规律总结
6. 关键发现和趋势识别

要求：
- 基于真实数据和聚类结果
- 提供专业的案件分析视角
- 以连贯的文字形式输出，不要使用表格格式
- 内容简明扼要，控制在300-400字
- 不要在输出中显示字数统计
"""

            response = await self._call_llm(prompt)
            logger.info("盗窃案件分析完成")
            return response

        except Exception as e:
            logger.error(f"盗窃案件分析失败: {e}")
            return "盗窃案件分析过程中出现异常，请检查数据质量。"


    async def analyze_vice_locations(self, prostitution_df, gambling_df) -> Dict[str, Any]:
        """涉黄涉赌场所提取分析 - 单条分析后汇总"""
        try:
            # 初始化统计字典
            locations_count = {}

            # 合并所有案例
            all_cases = []

            # 收集涉黄案例
            if not prostitution_df.empty:
                for _, row in prostitution_df.iterrows():
                    all_cases.append({
                        "type": "涉黄",
                        "jqxz": str(row.get('jqxz', '')),
                        "cjqk": str(row.get('cjqk', ''))
                    })

            # 收集涉赌案例
            if not gambling_df.empty:
                for _, row in gambling_df.iterrows():
                    all_cases.append({
                        "type": "涉赌",
                        "jqxz": str(row.get('jqxz', '')),
                        "cjqk": str(row.get('cjqk', ''))
                    })

            if not all_cases:
                return {"locations": {}}

            # 单条分析每个案件
            for case in all_cases:
                cjqk = case['cjqk'].strip()
                jqxz = case['jqxz'].strip()

                # 过滤空数据
                if len(cjqk) < 10:
                    continue

                # 单条分析提示词 - 极简格式
                prompt = f"""
{case['type']}案件：{cjqk[:80]}

从以下选项选择场所类型：
足浴按摩类、酒店宾馆类、棋牌娱乐类、出租屋类、其他

只返回JSON：{{"location": "场所类型"}}
"""

                try:
                    response = await self._call_llm(prompt)
                    result = self._extract_json_from_response(response)

                    if isinstance(result, dict):
                        # 统计场所类型
                        location = result.get('location', '其他')
                        if location in locations_count:
                            locations_count[location] += 1
                        else:
                            locations_count[location] = 1

                except Exception as e:
                    logger.warning(f"单条涉黄涉赌案件分析失败: {e}")
                    # 分析失败的归入"其他"类别
                    locations_count['其他'] = locations_count.get('其他', 0) + 1

            logger.info(f"涉黄涉赌场所分析完成，共分析{len(all_cases)}条案件")
            return {"locations": locations_count}

        except Exception as e:
            logger.error(f"涉黄涉赌场所分析失败: {e}")
            return {"locations": {}}

    async def analyze_vice_deep(self, prostitution_df, gambling_df, location_result) -> str:
        """涉黄涉赌分析"""
        try:
            # 收集案例样本 - 使用智能采样
            case_samples = []
            if not prostitution_df.empty:
                prostitution_samples = self._get_smart_samples(prostitution_df, 'cjqk', max_tokens=1500, min_samples=5)
                case_samples.extend([f"[涉黄] {case}" for case in prostitution_samples])
            if not gambling_df.empty:
                gambling_samples = self._get_smart_samples(gambling_df, 'cjqk', max_tokens=1500, min_samples=5)
                case_samples.extend([f"[涉赌] {case}" for case in gambling_samples])

            if not case_samples:
                return "暂无涉黄涉赌案件数据可供分析。"

            # 处理场所分析结果
            if isinstance(location_result, dict):
                # 如果是字典格式，提取相关信息
                locations_info = location_result.get('locations', {})
                summary_info = location_result.get('summary', '')

                location_text = f"""
场所类型分布：{locations_info}
场所分析总结：{summary_info}
"""
            else:
                # 如果是字符串格式，直接使用
                location_text = str(location_result)

            prompt = f"""
请对涉黄涉赌案件进行专业分析：

基础数据：涉黄 {len(prostitution_df)}起，涉赌 {len(gambling_df)}起

场所分布：{location_text}

分析要求：
1. 活动特点和规律
2. 主要场所类型分析
3. 社会危害和影响
4. 执法重点和难点

输出要求：
- 专业执法分析视角
- 连贯文字段落形式
- 简明扼要，重点突出
- 控制在250-350字
- 不要显示思考过程
- 不要显示字数统计
"""

            response = await self._call_llm(prompt)
            logger.info("涉黄涉赌分析完成")
            return response

        except Exception as e:
            logger.error(f"涉黄涉赌分析失败: {e}")
            return "涉黄涉赌分析过程中出现异常，请检查数据质量。"

    async def generate_vice_measures(self, prostitution_df, gambling_df, analysis_result: str) -> str:
        """生成涉黄涉赌防范措施建议"""
        try:
            prompt = f"""
基于以下涉黄涉赌案件分析结果，提出针对性的防范和打击措施建议：

案件情况：涉黄 {len(prostitution_df)}起，涉赌 {len(gambling_df)}起
分析结果：{analysis_result}

请提出以下方面的具体措施建议：
1. 特定场所巡查和监管措施
2. 联合整治行动建议
3. 宣传教育和预防措施
4. 线索摸排和情报收集
5. 技术手段和工作方法
6. 长效机制建设建议

要求：
- 措施具体可操作
- 针对性强，结合实际情况
- 体现预防和打击并重
- 适合基层公安机关实施
- 不要在输出中显示字数统计
"""

            response = await self._call_llm(prompt)
            logger.info("涉黄涉赌措施建议生成完成")
            return response

        except Exception as e:
            logger.error(f"涉黄涉赌措施建议生成失败: {e}")
            return "基于当前案件情况，建议加强重点场所巡查，完善联合执法机制，强化宣传教育，建立长效监管体系。"

    async def analyze_fight_locations(self, fight_df) -> Dict[str, Any]:
        """打架斗殴场所提取分析 - 单条分析后汇总"""
        try:
            if fight_df.empty:
                return {"locations": {}}

            # 初始化统计字典
            locations_count = {}

            # 单条分析每个案件
            for _, row in fight_df.iterrows():
                cjqk = str(row.get('cjqk', '')).strip()
                jqxz = str(row.get('jqxz', '')).strip()

                # 过滤空数据
                if len(cjqk) < 10:
                    continue

                # 单条分析提示词 - 极简格式
                prompt = f"""
打架斗殴案件：{cjqk[:80]}

从以下选项选择场所类型：
餐饮场所、娱乐场所、街面道路、居住区域、其他

只返回JSON：{{"location": "场所类型"}}
"""

                try:
                    response = await self._call_llm(prompt)
                    result = self._extract_json_from_response(response)

                    if isinstance(result, dict):
                        # 统计场所类型
                        location = result.get('location', '其他')
                        if location in locations_count:
                            locations_count[location] += 1
                        else:
                            locations_count[location] = 1

                except Exception as e:
                    logger.warning(f"单条打架斗殴案件分析失败: {e}")
                    # 分析失败的归入"其他"类别
                    locations_count['其他'] = locations_count.get('其他', 0) + 1

            logger.info(f"打架斗殴场所分析完成，共分析{len(fight_df)}条案件")
            return {"locations": locations_count}

        except Exception as e:
            logger.error(f"打架斗殴场所分析失败: {e}")
            return {"locations": {}}


    async def analyze_fight_deep(self, fight_df, location_result) -> str:
        """打架斗殴分析"""
        try:
            if fight_df.empty:
                return "暂无打架斗殴案件数据可供分析。"

            # 获取时间分布
            hourly_dist = fight_df.groupby('hour').size().to_dict()
            peak_hours = sorted(hourly_dist.items(), key=lambda x: x[1], reverse=True)[:3]

            # 获取案例样本 - 使用智能采样
            case_samples = self._get_smart_samples(fight_df, 'cjqk', max_tokens=3000, min_samples=10)

            # 处理场所分析结果
            if isinstance(location_result, dict):
                # 如果是字典格式，提取相关信息
                locations_info = location_result.get('locations', {})
                summary_info = location_result.get('summary', '')

                location_text = f"""
场所类型分布：{locations_info}
场所分析总结：{summary_info}
"""
            else:
                # 如果是字符串格式，直接使用
                location_text = str(location_result)

            prompt = f"""
基于以下打架斗殴案件数据进行分析：

案件总数：{len(fight_df)}起

场所分析结果：
{location_text}

高发时段：{[f"{h}时({c}起)" for h, c in peak_hours]}

案例样本：
{chr(10).join(case_samples)}

请从以下角度进行分析：
1. 事件起因分析（如口角纠纷、醉酒滋事、经济利益冲突等）
2. 高发时段特征分析
3. 高发场所特征分析
4. 参与人员特点分析
5. 事件规律和特征总结
6. 关键发现和趋势识别

要求：
- 基于真实案例和场所分析结果
- 提供专业的治安分析视角
- 以连贯的文字形式输出，不要使用表格格式
- 内容简明扼要，控制在300-400字
- 不要在输出中显示字数统计
"""

            response = await self._call_llm(prompt)
            logger.info("打架斗殴分析完成")
            return response

        except Exception as e:
            logger.error(f"打架斗殴分析失败: {e}")
            return "打架斗殴分析过程中出现异常，请检查数据质量。"

    async def generate_fight_measures(self, fight_df, analysis_result: str) -> str:
        """生成打架斗殴预防措施建议"""
        try:
            prompt = f"""
基于以下打架斗殴案件分析结果，提出针对性的预防和处置措施建议：

案件情况：打架斗殴 {len(fight_df)}起
分析结果：{analysis_result}

请提出以下方面的具体措施建议：
1. 重点场所安保和监管措施
2. 高峰期巡逻和防控策略
3. 快速反应和处置机制
4. 纠纷调解前置措施
5. 法制宣传和教育活动
6. 社会治安综合治理建议

要求：
- 措施具体可操作
- 针对性强，结合实际情况
- 体现预防为主的理念
- 适合基层公安机关实施
- 不要在输出中显示字数统计
"""

            response = await self._call_llm(prompt)
            logger.info("打架斗殴措施建议生成完成")
            return response

        except Exception as e:
            logger.error(f"打架斗殴措施建议生成失败: {e}")
            return "基于当前案件情况，建议加强重点场所巡查，完善快速反应机制，强化纠纷调解，加大法制宣传力度。"

    async def generate_comprehensive_recommendations(self,
                                                   criminal_df, security_df, theft_df,
                                                   prostitution_df, gambling_df, fight_df,
                                                   analysis_results: Dict[str, str]) -> str:
        """生成综合防范建议"""
        try:
            # 统计各类案件数量
            case_counts = {
                "刑事案件": len(criminal_df),
                "治安案件": len(security_df),
                "盗窃案件": len(theft_df),
                "涉黄警情": len(prostitution_df),
                "涉赌警情": len(gambling_df),
                "打架斗殴警情": len(fight_df)
            }

            # 提取分析结果摘要
            analysis_summary = []
            for key, content in analysis_results.items():
                if content and len(content) > 100:
                    # 提取前200字符作为摘要
                    summary = content[:200] + "..."
                    analysis_summary.append(f"{key}: {summary}")

            prompt = f"""
基于以下警情数据分析结果，请提出简明扼要的综合防范建议：

案件统计：
{chr(10).join([f"- {k}: {v}起" for k, v in case_counts.items() if v > 0])}

请从以下角度提出针对性建议：
1. 重点场所和时段防控
2. 宣传教育和预防措施
3. 部门协作和应急处置

要求：
- 建议简明扼要，具体可操作
- 结合案件特点，针对性强
- 体现预防为主、打防结合
- 适合基层公安机关实施
- 连贯文字形式，不要表格格式
- 不要在内容中显示字数统计
"""

            response = await self._call_llm(prompt)
            logger.info("综合防范建议生成完成")
            return response

        except Exception as e:
            logger.error(f"综合防范建议生成失败: {e}")
            return """基于当前警情分析，建议采取以下防范措施：一是加强重点时段和场所巡逻防控；二是强化宣传教育，提高群众防范意识；三是建立部门协作机制，形成打防结合格局；四是完善应急处置预案，提高快速反应能力。"""

    async def generate_comprehensive_llm_analysis(self, data_service, start_time: str, end_time: str, fkdwdm: Optional[str] = None) -> Dict[str, Any]:
        """生成全面的LLM分析内容"""
        try:
            logger.info("开始生成全面LLM分析")
            llm_results = {}

            # 1. 获取各类案件数据
            criminal_df = data_service.get_specific_police_data(start_time, end_time, "刑事案件", fkdwdm)
            security_df = data_service.get_specific_police_data(start_time, end_time, "治安案件", fkdwdm)
            theft_df = data_service.get_specific_police_data(start_time, end_time, "盗窃案件", fkdwdm)
            prostitution_df = data_service.get_specific_police_data(start_time, end_time, "涉黄警情", fkdwdm)
            gambling_df = data_service.get_specific_police_data(start_time, end_time, "涉赌警情", fkdwdm)
            fight_df = data_service.get_specific_police_data(start_time, end_time, "打架斗殴警情", fkdwdm)

            # 2. 刑事案件与治安案件分析
            if not criminal_df.empty or not security_df.empty:
                # 综合分析
                llm_results["criminal_security_deep_analysis"] = await self.analyze_criminal_security_deep(
                    criminal_df, security_df
                )

                # 单独分析
                if not criminal_df.empty:
                    llm_results["criminal_analysis"] = await self.analyze_criminal_cases_separate(criminal_df)

                if not security_df.empty:
                    llm_results["security_analysis"] = await self.analyze_security_cases_separate(security_df)

            # 3. 盗窃案件分析
            if not theft_df.empty:
                # 3.1 聚类分析
                theft_clustering = await self.analyze_theft_clustering(theft_df)
                llm_results["theft_clustering"] = theft_clustering

                # 3.2 综合分析
                llm_results["theft_deep_analysis"] = await self.analyze_theft_deep(
                    theft_df, theft_clustering
                )

            # 4. 涉黄涉赌分析
            if not prostitution_df.empty or not gambling_df.empty:
                # 4.1 场所提取
                vice_locations = await self.analyze_vice_locations(prostitution_df, gambling_df)
                llm_results["vice_locations"] = vice_locations

                # 4.2 综合分析
                llm_results["vice_deep_analysis"] = await self.analyze_vice_deep(
                    prostitution_df, gambling_df, vice_locations
                )

            # 5. 打架斗殴分析
            if not fight_df.empty:
                # 5.1 场所提取
                fight_locations = await self.analyze_fight_locations(fight_df)
                llm_results["fight_locations"] = fight_locations

                # 5.2 综合分析
                llm_results["fight_deep_analysis"] = await self.analyze_fight_deep(
                    fight_df, fight_locations
                )

            # 6. 生成综合防范建议
            logger.info("正在生成综合防范建议...")
            llm_results["comprehensive_recommendations"] = await self.generate_comprehensive_recommendations(
                criminal_df, security_df, theft_df, prostitution_df, gambling_df, fight_df, llm_results
            )

            logger.info("全面LLM分析完成")
            return llm_results

        except Exception as e:
            logger.error(f"全面LLM分析失败: {e}")
            return {"error": "LLM分析过程中出现异常"}


# 全局LLM服务实例
llm_service = LLMService()
