#!/bin/bash

# Docker容器启动脚本
# 支持批量生成历史报告和定时任务两种模式

set -e

echo "=========================================="
echo "月报告生成系统 Docker 启动"
echo "=========================================="

# 启动cron服务
echo "启动cron服务..."
service cron start

# 检查运行模式
MODE=${RUN_MODE:-"batch"}
echo "运行模式: $MODE"

case "$MODE" in
    "batch")
        echo "批量模式：生成2025年1-6月历史报告"
        python generate_monthly_reports.py batch
        ;;
    "monthly")
        echo "月度模式：生成上个月报告"
        python generate_monthly_reports.py monthly
        ;;
    "custom")
        echo "自定义模式：生成指定年月报告"
        if [ -z "$CUSTOM_YEAR" ] || [ -z "$CUSTOM_MONTHS" ]; then
            echo "错误：自定义模式需要设置 CUSTOM_YEAR 和 CUSTOM_MONTHS 环境变量"
            exit 1
        fi
        python generate_monthly_reports.py custom $CUSTOM_YEAR $CUSTOM_MONTHS
        ;;
    "cron")
        echo "定时任务模式：设置cron并保持运行"
        
        # 设置定时任务（每月3号凌晨2点）
        echo "设置定时任务..."
        echo "0 2 3 * * cd /app && python generate_monthly_reports.py monthly >> /var/log/monthly_reports.log 2>&1" > /tmp/crontab
        crontab /tmp/crontab
        
        echo "定时任务已设置："
        crontab -l
        
        # 如果设置了INITIAL_BATCH=true，先运行一次批量生成
        if [ "$INITIAL_BATCH" = "true" ]; then
            echo "执行初始批量生成..."
            python generate_monthly_reports.py batch
        fi
        
        # 保持容器运行，监控cron日志
        echo "容器将保持运行，等待定时任务执行..."
        echo "查看日志: docker logs -f <container_name>"
        
        # 创建日志文件
        touch /var/log/monthly_reports.log
        
        # 保持容器运行并显示日志
        tail -f /var/log/monthly_reports.log &
        
        # 保持cron服务运行
        while true; do
            sleep 60
            # 检查cron服务状态
            if ! pgrep cron > /dev/null; then
                echo "cron服务停止，重新启动..."
                service cron start
            fi
        done
        ;;
    "daemon")
        echo "守护进程模式：启动Web服务和定时任务"
        
        # 设置定时任务
        echo "0 2 3 * * cd /app && python generate_monthly_reports.py monthly >> /var/log/monthly_reports.log 2>&1" > /tmp/crontab
        crontab /tmp/crontab
        
        # 如果设置了INITIAL_BATCH=true，先运行一次批量生成
        if [ "$INITIAL_BATCH" = "true" ]; then
            echo "执行初始批量生成..."
            python generate_monthly_reports.py batch
        fi
        
        # 启动Web服务（如果有main.py或start.py）
        if [ -f "main.py" ]; then
            echo "启动Web服务..."
            python main.py &
        elif [ -f "start.py" ]; then
            echo "启动Web服务..."
            python start.py &
        fi
        
        # 创建日志文件
        touch /var/log/monthly_reports.log
        
        # 保持容器运行
        tail -f /var/log/monthly_reports.log &
        
        while true; do
            sleep 60
            if ! pgrep cron > /dev/null; then
                echo "cron服务停止，重新启动..."
                service cron start
            fi
        done
        ;;
    *)
        echo "未知的运行模式: $MODE"
        echo "支持的模式:"
        echo "  batch   - 批量生成2025年1-6月报告"
        echo "  monthly - 生成上个月报告"
        echo "  custom  - 自定义年月（需要CUSTOM_YEAR和CUSTOM_MONTHS环境变量）"
        echo "  cron    - 设置定时任务并保持运行"
        echo "  daemon  - 守护进程模式（Web服务+定时任务）"
        exit 1
        ;;
esac

echo "=========================================="
echo "执行完成"
echo "=========================================="
