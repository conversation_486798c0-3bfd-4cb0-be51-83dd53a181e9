"""
警情数据分析报告生成系统主入口 - 修复版
"""
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from config import settings
from app.api import router as api_router
from utils.logger import setup_logger
import os

# 初始化日志
setup_logger()

# 创建输出目录
os.makedirs(settings.report_output_dir, exist_ok=True)
os.makedirs(settings.chart_output_dir, exist_ok=True)
os.makedirs("logs", exist_ok=True)
os.makedirs("static", exist_ok=True)

# 创建FastAPI应用
app = FastAPI(
    title="警情数据分析报告生成系统",
    description="基于数据库警情数据，使用大数据处理+LLM生成多维分析报告",
    version="1.0.0",
    docs_url=None,  # 禁用标准Swagger UI
    redoc_url=None  # 禁用标准ReDoc
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory="static"), name="static")

# 注册路由
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "警情数据分析报告生成系统",
        "version": "1.0.0",
        "docs": "/docs",
        "description": "基于数据库警情数据，使用大数据处理+LLM生成多维分析报告",
        "features": [
            "数据分析报告生成",
            "中文图表支持",
            "同比环比分析",
            "Word文档导出"
        ]
    }


@app.get("/docs", include_in_schema=False)
async def simple_api_docs():
    """简单的API文档页面"""
    html_content = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>警情数据分析报告生成系统 - API文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .api-section {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .api-section h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .endpoint {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .endpoint.post {
            border-left-color: #007bff;
        }

        .endpoint.get {
            border-left-color: #28a745;
        }

        .method {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }

        .method.get {
            background: #28a745;
        }

        .method.post {
            background: #007bff;
        }

        .url {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
        }

        .test-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-top: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        .btn:hover {
            background: #5a6fd8;
        }

        .response {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .status-links {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .status-link {
            padding: 10px 20px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .status-link:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>警情数据分析报告生成系统</h1>
            <p>基于数据库警情数据，使用大数据处理+LLM生成多维分析报告</p>
        </div>

        <div class="api-section">
            <h2>🔗 快速链接</h2>
            <div class="status-links">
                <a href="/health" class="status-link" target="_blank">健康检查</a>
                <a href="/api/v1/system-status" class="status-link" target="_blank">系统状态</a>
                <a href="/api/v1/test-connection" class="status-link" target="_blank">数据库连接测试</a>
            </div>
        </div>

        <div class="api-section">
            <h2>📊 主要API接口</h2>

            <div class="endpoint post">
                <div>
                    <span class="method post">POST</span>
                    <span class="url">/api/v1/generate-report</span>
                </div>
                <h4>生成数据分析报告</h4>
                <p>根据指定时间范围生成包含图表和分析文本的Word报告</p>

                <div class="test-form">
                    <h5>测试接口</h5>
                    <div class="form-group">
                        <label>开始时间:</label>
                        <input type="datetime-local" id="start_time" value="2023-01-01T00:00">
                    </div>
                    <div class="form-group">
                        <label>结束时间:</label>
                        <input type="datetime-local" id="end_time" value="2023-01-31T23:59">
                    </div>
                    <div class="form-group">
                        <label>派出所代码 (可选):</label>
                        <input type="text" id="fkdwdm" placeholder="例如: 110101 (留空表示所有派出所)">
                    </div>
                    <button class="btn" onclick="generateReport()">生成报告</button>
                    <div id="report_response" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="endpoint get">
                <div>
                    <span class="method get">GET</span>
                    <span class="url">/api/v1/data-preview</span>
                </div>
                <h4>数据预览</h4>
                <p>预览指定时间范围内的警情数据</p>

                <div class="test-form">
                    <h5>测试接口</h5>
                    <div class="form-group">
                        <label>开始时间:</label>
                        <input type="datetime-local" id="preview_start" value="2023-01-01T00:00">
                    </div>
                    <div class="form-group">
                        <label>结束时间:</label>
                        <input type="datetime-local" id="preview_end" value="2023-01-02T00:00">
                    </div>
                    <div class="form-group">
                        <label>限制条数:</label>
                        <input type="number" id="preview_limit" value="10" min="1" max="100">
                    </div>
                    <div class="form-group">
                        <label>派出所代码 (可选):</label>
                        <input type="text" id="preview_fkdwdm" placeholder="例如: 110101 (留空表示所有派出所)">
                    </div>
                    <button class="btn" onclick="previewData()">预览数据</button>
                    <div id="preview_response" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="endpoint get">
                <div>
                    <span class="method get">GET</span>
                    <span class="url">/api/v1/download-report/{filename}</span>
                </div>
                <h4>下载报告</h4>
                <p>下载已生成的报告文件</p>

                <div class="test-form">
                    <h5>测试接口</h5>
                    <div class="form-group">
                        <label>文件名:</label>
                        <input type="text" id="download_filename" placeholder="例如: 警情分析报告_20250706_102734.docx">
                    </div>
                    <button class="btn" onclick="downloadReport()">下载报告</button>
                </div>
            </div>
        </div>

        <div class="api-section">
            <h2>📋 使用说明</h2>
            <ol>
                <li><strong>生成报告</strong>：选择时间范围，点击"生成报告"按钮</li>
                <li><strong>数据预览</strong>：可以先预览数据确认时间范围内有数据</li>
                <li><strong>下载报告</strong>：报告生成后，使用返回的文件名下载Word文档</li>
                <li><strong>报告内容</strong>：包含多维度数据分析、图表、同比环比分析等</li>
                <li><strong>派出所筛选</strong>：可选填写派出所代码(fkdwdm)来生成特定派出所的报告，留空则分析所有派出所数据</li>
            </ol>

            <h3>📝 参数说明</h3>
            <ul>
                <li><strong>start_time / end_time</strong>：时间范围，格式为 YYYY-MM-DD HH:MM:SS</li>
                <li><strong>fkdwdm</strong>：派出所代码，可选参数。用于筛选特定派出所的数据进行分析，留空则分析所有派出所</li>
                <li><strong>limit</strong>：数据预览时的限制条数，默认10条</li>
            </ul>

            <h3>💡 提示</h3>
            <ul>
                <li>建议先使用"数据预览"功能确认时间范围内有数据</li>
                <li>生成报告可能需要几分钟时间，请耐心等待</li>
                <li>派出所代码(fkdwdm)可以从数据预览结果中获取</li>
            </ul>
        </div>
    </div>

    <script>
        async function generateReport() {
            const startTime = document.getElementById('start_time').value.replace('T', ' ') + ':00';
            const endTime = document.getElementById('end_time').value.replace('T', ' ') + ':59';
            const fkdwdm = document.getElementById('fkdwdm').value.trim();
            const responseDiv = document.getElementById('report_response');

            responseDiv.style.display = 'block';
            responseDiv.textContent = '正在生成报告，请稍候...';

            try {
                const requestBody = {
                    start_time: startTime,
                    end_time: endTime
                };

                // 只有当fkdwdm不为空时才添加到请求体中
                if (fkdwdm) {
                    requestBody.fkdwdm = fkdwdm;
                }

                const response = await fetch('/api/v1/generate-report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseDiv.textContent = '错误: ' + error.message;
            }
        }

        async function previewData() {
            const startTime = document.getElementById('preview_start').value.replace('T', ' ') + ':00';
            const endTime = document.getElementById('preview_end').value.replace('T', ' ') + ':59';
            const limit = document.getElementById('preview_limit').value;
            const fkdwdm = document.getElementById('preview_fkdwdm').value.trim();
            const responseDiv = document.getElementById('preview_response');

            responseDiv.style.display = 'block';
            responseDiv.textContent = '正在加载数据...';

            try {
                let url = `/api/v1/data-preview?start_time=${encodeURIComponent(startTime)}&end_time=${encodeURIComponent(endTime)}&limit=${limit}`;

                // 只有当fkdwdm不为空时才添加到URL中
                if (fkdwdm) {
                    url += `&fkdwdm=${encodeURIComponent(fkdwdm)}`;
                }

                const response = await fetch(url);
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseDiv.textContent = '错误: ' + error.message;
            }
        }

        function downloadReport() {
            const filename = document.getElementById('download_filename').value;
            if (!filename) {
                alert('请输入文件名');
                return;
            }

            const url = `/api/v1/download-report/${encodeURIComponent(filename)}`;
            window.open(url, '_blank');
        }
    </script>
</body>
</html>"""

    return HTMLResponse(content=html_content)





@app.get("/debug-static")
async def debug_static_files():
    """调试静态文件"""
    from pathlib import Path

    static_dir = Path("static")
    files_info = []

    if static_dir.exists():
        for file_path in static_dir.iterdir():
            if file_path.is_file():
                files_info.append({
                    "name": file_path.name,
                    "size": file_path.stat().st_size,
                    "url": f"/static/{file_path.name}"
                })

    return {
        "static_directory": str(static_dir.absolute()),
        "directory_exists": static_dir.exists(),
        "files": files_info,
        "total_files": len(files_info)
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.app_host,
        port=settings.app_port,
        reload=settings.app_debug
    )
