services:
  # 批量生成历史报告（一次性运行）
  monthly-reports-batch:
    build: .
    image: monthly-reports:latest
    container_name: monthly-reports-batch
    environment:
      # 运行模式
      - RUN_MODE=batch
      # 数据库配置
      - DB_HOST=*************
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=jkga@qzlq
      - DB_NAME=llm
      # LLM服务配置
      - LLM_URL=http://************:8889
      - LLM_API_KEY=sk-HBE4QnJ910R7ntO1421c91CdA56a40589aB1B72f3524C460
      - LLM_MODEL=Qwen3-235B-A22B
      # 应用配置
      - APP_HOST=0.0.0.0
      - APP_PORT=1998
      - APP_DEBUG=false
    volumes:
      # 挂载报告输出目录到宿主机
      - ./output/reports:/app/reports
      - ./output/charts:/app/charts
      - ./output/logs:/app/logs
    networks:
      - app-network
    profiles:
      - batch

  # 定时任务服务（持续运行）
  monthly-reports-cron:
    build: .
    image: monthly-reports:latest
    container_name: monthly-reports-cron
    environment:
      # 运行模式
      - RUN_MODE=cron
      # 是否在启动时执行批量生成（可选）
      - INITIAL_BATCH=false
      # 数据库配置
      - DB_HOST=*************
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=jkga@qzlq
      - DB_NAME=llm
      # LLM服务配置
      - LLM_URL=http://************:8889
      - LLM_API_KEY=sk-HBE4QnJ910R7ntO1421c91CdA56a40589aB1B72f3524C460
      - LLM_MODEL=Qwen3-235B-A22B
      # 应用配置
      - APP_HOST=0.0.0.0
      - APP_PORT=1998
      - APP_DEBUG=false
    volumes:
      # 挂载报告输出目录到宿主机
      - ./output/reports:/app/reports
      - ./output/charts:/app/charts
      - ./output/logs:/app/logs
    restart: unless-stopped
    networks:
      - app-network
    profiles:
      - cron

  # 完整服务（Web服务+定时任务）
  monthly-reports-daemon:
    build: .
    image: monthly-reports:latest
    container_name: monthly-reports-daemon
    environment:
      # 运行模式
      - RUN_MODE=daemon
      # 是否在启动时执行批量生成
      - INITIAL_BATCH=true
      # 数据库配置
      - DB_HOST=*************
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=jkga@qzlq
      - DB_NAME=llm
      # LLM服务配置
      - LLM_URL=http://************:8889
      - LLM_API_KEY=sk-HBE4QnJ910R7ntO1421c91CdA56a40589aB1B72f3524C460
      - LLM_MODEL=Qwen3-235B-A22B
      # 应用配置
      - APP_HOST=0.0.0.0
      - APP_PORT=1998
      - APP_DEBUG=false
    volumes:
      # 挂载报告输出目录到宿主机
      - ./output/reports:/app/reports
      - ./output/charts:/app/charts
      - ./output/logs:/app/logs
    ports:
      - "1998:1998"
    restart: unless-stopped
    networks:
      - app-network
    profiles:
      - daemon

networks:
  app-network:
    driver: bridge
