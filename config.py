"""
应用配置模块
"""
from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """应用配置类"""
    
    # 数据库配置
    db_host: str = "*************"
    db_port: int = 3306
    db_user: str = "root"
    db_password: str = "jkga@qzlq"
    db_name: str = "llm"
    db_table: str = "p_translation_feedback_data"
    
    # LLM服务配置
    llm_url: str = "http://************:8889"
    llm_api_key: str = "sk-HBE4QnJ910R7ntO1421c91CdA56a40589aB1B72f3524C460"
    llm_model: str = "Qwen3-235B-A22B"
    
    # 应用配置
    app_host: str = "0.0.0.0"
    app_port: int = 1998
    app_debug: bool = True
    
    # 报告配置
    report_output_dir: str = "./reports"
    chart_output_dir: str = "./charts"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()
