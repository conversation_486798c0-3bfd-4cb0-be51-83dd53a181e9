# 月报告生成系统使用说明

## 概述

月报告生成系统支持三种运行模式：
1. **批量模式**：一次性生成2025年1-6月的所有报告
2. **月度模式**：每月3号自动生成上个月的报告
3. **自定义模式**：手动指定年份和月份生成报告

## 运行模式详解

### 1. 批量模式（默认）
生成2025年1-6月的所有报告，适用于初始化或补充历史数据。

```bash
# 方式1：直接运行（默认批量模式）
python generate_monthly_reports.py

# 方式2：明确指定批量模式
python generate_monthly_reports.py batch
```

### 2. 月度模式
生成上个月的报告，适用于定时任务。

```bash
# 手动运行月度模式
python generate_monthly_reports.py monthly
```

**自动检测**：如果今天是3号且没有指定模式，脚本会自动切换到月度模式。

### 3. 自定义模式
手动指定年份和月份。

```bash
# 生成2025年7月的报告
python generate_monthly_reports.py custom 2025 7

# 生成2025年7,8,9月的报告
python generate_monthly_reports.py custom 2025 7,8,9

# 生成2024年12月的报告
python generate_monthly_reports.py custom 2024 12
```

## 定时任务设置

### 自动设置定时任务

运行定时任务设置工具：

```bash
python setup_monthly_cron.py
```

该工具提供以下功能：
1. 设置定时任务（每月3号凌晨2点）
2. 查看当前定时任务
3. 移除定时任务
4. 测试月度运行

### 手动设置定时任务

如果需要手动设置，可以编辑crontab：

```bash
crontab -e
```

添加以下行：
```
# 每月3号凌晨2点生成上个月报告
0 2 3 * * cd /path/to/your/project && python generate_monthly_reports.py monthly >> /var/log/monthly_reports.log 2>&1
```

## 派出所列表

系统会为以下派出所生成报告：
- 330499510000
- 330499520000
- 330499530000
- 330499540000
- 330499550000

## 数据库保存

每个报告生成后会在 `p_pcs_reports` 表中保存记录：

| 字段 | 说明 | 示例 |
|------|------|------|
| fkdwdm | 派出所代码 | 330499510000 |
| year | 年份 | 2025 |
| month | 月份 | 7 |
| name | 文件名 | 嘉兴开发区城南派出所分析报告2025年7月1日-7月31日.docx |
| created_at | 创建时间 | 2025-08-03 02:00:00 |

## 运行示例

### 示例1：初次部署
```bash
# 1. 生成历史数据（2025年1-6月）
python generate_monthly_reports.py batch

# 2. 设置定时任务
python setup_monthly_cron.py
# 选择选项1设置定时任务
```

### 示例2：每月3号自动运行
```bash
# 系统会在每月3号凌晨2点自动执行：
python generate_monthly_reports.py monthly
```

### 示例3：补充特定月份
```bash
# 补充生成2025年7月的报告
python generate_monthly_reports.py custom 2025 7
```

## 日志和监控

### 查看定时任务日志
```bash
tail -f /var/log/monthly_reports.log
```

### 查看实时运行状态
```bash
# 查看当前运行的Python进程
ps aux | grep generate_monthly_reports

# 查看系统资源使用
top -p $(pgrep -f generate_monthly_reports)
```

## 故障排除

### 1. 定时任务未执行
```bash
# 检查cron服务状态
sudo systemctl status cron

# 查看cron日志
sudo tail -f /var/log/syslog | grep CRON

# 检查定时任务是否存在
crontab -l | grep monthly_reports
```

### 2. 报告生成失败
```bash
# 查看详细错误日志
tail -100 /var/log/monthly_reports.log

# 手动测试运行
python generate_monthly_reports.py monthly
```

### 3. 数据库连接问题
```bash
# 测试数据库连接
python -c "from database.connection import db_manager; print(db_manager.test_connection())"
```

## 时间计算逻辑

### 月度模式的时间计算
- **8月3日运行**：生成7月报告（2025-07-01 至 2025-07-31）
- **9月3日运行**：生成8月报告（2025-08-01 至 2025-08-31）
- **1月3日运行**：生成12月报告（2024-12-01 至 2024-12-31）

### 文件命名规则
```
{派出所名称}分析报告{年份}年{月份}月{开始日期}-{结束日期}.docx

示例：
嘉兴开发区城南派出所分析报告2025年7月1日-7月31日.docx
```

## 注意事项

1. **权限要求**：确保脚本有写入reports目录和数据库的权限
2. **磁盘空间**：每个报告约1-5MB，注意磁盘空间
3. **运行时间**：每个报告生成约需要2-5分钟，总计约10-25分钟
4. **数据依赖**：确保数据库中有对应月份的警情数据
5. **LLM服务**：确保LLM服务正常运行

## 维护建议

1. **定期检查**：每月检查定时任务是否正常执行
2. **日志清理**：定期清理 `/var/log/monthly_reports.log`
3. **文件清理**：根据需要清理旧的报告文件
4. **数据库维护**：定期检查数据库表的大小和性能
