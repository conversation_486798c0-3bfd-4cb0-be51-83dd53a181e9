"""
FastAPI路由模块
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
import asyncio
import os
from datetime import datetime
from typing import Dict, Any
from loguru import logger

from models.police_data import ReportRequest, ReportResponse
from services.data_service import data_service
from services.analysis_engine import analysis_engine
from services.chart_service import enhanced_chart_service
from services.llm_service import llm_service
from services.report_service import report_service
from database.connection import db_manager

router = APIRouter()


@router.post("/generate-report", response_model=ReportResponse)
async def generate_report(request: ReportRequest, background_tasks: BackgroundTasks):
    """生成警情分析报告"""
    try:
        logger.info(f"开始生成报告: {request.start_time} - {request.end_time}")
        
        # 1. 验证时间格式
        try:
            start_dt = datetime.strptime(request.start_time, '%Y-%m-%d %H:%M:%S')
            end_dt = datetime.strptime(request.end_time, '%Y-%m-%d %H:%M:%S')
            
            if start_dt >= end_dt:
                raise HTTPException(status_code=400, detail="开始时间必须早于结束时间")
                
        except ValueError:
            raise HTTPException(status_code=400, detail="时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式")
        
        # 2. 测试数据库连接
        if not db_manager.test_connection():
            raise HTTPException(status_code=500, detail="数据库连接失败")
        
        # 3. 获取数据
        logger.info("正在获取警情数据...")
        df = data_service.get_police_data(request.start_time, request.end_time, request.fkdwdm)
        
        if df.empty:
            raise HTTPException(status_code=404, detail="指定时间范围内未找到数据")
        
        # 4. 数据分析
        logger.info("正在进行数据分析...")
        analysis_result = analysis_engine.comprehensive_analysis(df)

        # 4.1 同比环比分析
        logger.info("正在进行同比环比分析...")
        comparison_analysis = data_service.get_multi_period_analysis(
            request.start_time, request.end_time, request.fkdwdm
        )
        analysis_result["comparison_analysis"] = comparison_analysis

        # 4.2 专项分析
        logger.info("正在进行专项分析...")
        special_analysis = analysis_engine.comprehensive_special_analysis(
            data_service, request.start_time, request.end_time, request.fkdwdm
        )

        # 4.3 获取总警情概况统计（包含同比环比）
        logger.info("正在获取总警情概况...")
        overview_stats = data_service.get_police_overview_with_comparison(
            request.start_time, request.end_time, request.fkdwdm
        )

        # 5. 生成图表
        logger.info("正在生成图表...")
        chart_paths = enhanced_chart_service.generate_required_charts(
            analysis_result, data_service, request.start_time, request.end_time, request.fkdwdm
        )
        
        # 6. 生成全面LLM深度分析内容
        logger.info("正在生成全面LLM深度分析...")
        time_range = f"{request.start_time} 至 {request.end_time}"

        # 生成全面的LLM分析内容
        llm_content = await llm_service.generate_comprehensive_llm_analysis(
            data_service, request.start_time, request.end_time, request.fkdwdm
        )

        # 注意：已移除旧的基础LLM内容生成方法，现在只使用深度分析功能

        # 7. 生成全面深度分析Word报告
        logger.info("正在生成全面深度分析Word报告...")
        report_path = report_service.generate_comprehensive_report(
            analysis_result, chart_paths, llm_content, special_analysis,
            overview_stats, time_range, request.fkdwdm
        )
        
        # 8. 准备响应数据
        data_summary = {
            'total_records': analysis_result['basic_stats']['total_records'],
            'date_range': analysis_result['basic_stats']['date_range'],
            'top_counties': list(analysis_result['geographic_analysis']['county_distribution'].keys())[:5],
            'top_categories': list(analysis_result['category_analysis']['category_distribution'].keys())[:5]
        }
        
        logger.info(f"报告生成成功: {report_path}")
        
        return ReportResponse(
            success=True,
            message="报告生成成功",
            report_path=report_path,
            data_summary=data_summary
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"报告生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"报告生成失败: {str(e)}")


@router.get("/download-report/{filename}")
async def download_report(filename: str):
    """下载报告文件"""
    try:
        file_path = os.path.join(report_service.output_dir, filename)
        
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件下载失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件下载失败: {str(e)}")


@router.get("/test-connection")
async def test_database_connection():
    """测试数据库连接"""
    try:
        success = db_manager.test_connection()
        
        if success:
            return {"status": "success", "message": "数据库连接正常"}
        else:
            return {"status": "failed", "message": "数据库连接失败"}
            
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return {"status": "error", "message": f"连接测试异常: {str(e)}"}


@router.get("/data-preview")
async def get_data_preview(start_time: str, end_time: str, limit: int = 10, fkdwdm: str = None):
    """预览数据"""
    try:
        # 验证时间格式
        try:
            datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
            datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            raise HTTPException(status_code=400, detail="时间格式错误")
        
        # 获取数据
        df = data_service.get_police_data(start_time, end_time, fkdwdm)
        
        if df.empty:
            return {"total_count": 0, "preview_data": []}
        
        # 返回预览数据
        preview_data = df.head(limit).to_dict('records')
        
        return {
            "total_count": len(df),
            "preview_data": preview_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"数据预览失败: {e}")
        raise HTTPException(status_code=500, detail=f"数据预览失败: {str(e)}")








@router.get("/system-status")
async def get_system_status():
    """获取系统状态"""
    try:
        # 检查数据库连接
        db_status = db_manager.test_connection()
        
        # 检查输出目录
        reports_dir_exists = os.path.exists(report_service.output_dir)
        charts_dir_exists = os.path.exists(enhanced_chart_service.output_dir)
        
        # 检查LLM服务（简单检查）
        llm_status = True  # 实际使用时可以发送测试请求
        
        return {
            "database_connection": db_status,
            "reports_directory": reports_dir_exists,
            "charts_directory": charts_dir_exists,
            "llm_service": llm_status,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"系统状态检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"系统状态检查失败: {str(e)}")
