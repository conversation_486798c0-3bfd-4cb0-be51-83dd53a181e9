#!/bin/bash

# Ubuntu服务器上的Docker部署脚本
# 支持批量生成历史报告和定时任务两种模式

set -e

# 配置变量
IMAGE_NAME="monthly-reports"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
TAR_FILE="${IMAGE_NAME}_${IMAGE_TAG}.tar"

echo "=========================================="
echo "月报告系统 Docker离线部署脚本"
echo "=========================================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装，请先安装Docker"
    echo "安装命令:"
    echo "  sudo apt update"
    echo "  sudo apt install docker.io docker-compose"
    echo "  sudo systemctl start docker"
    echo "  sudo systemctl enable docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose未安装，请先安装"
    echo "安装命令:"
    echo "  sudo apt install docker-compose"
    exit 1
fi

# 检查镜像文件是否存在
if [ ! -f "$TAR_FILE" ]; then
    echo "错误: 镜像文件 $TAR_FILE 不存在"
    echo "请确保在正确的目录中运行此脚本"
    exit 1
fi

# 检查docker-compose.yml是否存在
if [ ! -f "docker-compose.yml" ]; then
    echo "错误: docker-compose.yml 文件不存在"
    exit 1
fi

# 停止现有服务（如果存在）
echo "停止现有服务..."
sudo docker-compose --profile batch down 2>/dev/null || echo "没有运行中的批量服务"
sudo docker-compose --profile cron down 2>/dev/null || echo "没有运行中的定时服务"
sudo docker-compose --profile daemon down 2>/dev/null || echo "没有运行中的守护服务"

# 删除旧镜像（可选）
echo "清理旧镜像..."
sudo docker rmi "$FULL_IMAGE_NAME" 2>/dev/null || echo "没有找到旧镜像"

# 加载Docker镜像
echo "加载Docker镜像..."
echo "镜像文件: $TAR_FILE"
sudo docker load -i "$TAR_FILE"

if [ $? -eq 0 ]; then
    echo "✓ 镜像加载成功"
else
    echo "✗ 镜像加载失败"
    exit 1
fi

# 验证镜像是否加载成功
echo "验证镜像..."
if sudo docker images | grep -q "$IMAGE_NAME"; then
    echo "✓ 镜像验证成功"
    sudo docker images | grep "$IMAGE_NAME"
else
    echo "✗ 镜像验证失败"
    exit 1
fi

# 创建必要的目录
echo "创建输出目录..."
mkdir -p output/{reports,charts,logs}
sudo chown -R $USER:$USER output/

# 创建环境配置文件（如果不存在）
if [ ! -f ".env" ] && [ -f ".env.template" ]; then
    echo "创建环境配置文件..."
    cp .env.template .env
    echo "请根据需要编辑 .env 文件中的配置"
fi

# 显示部署选项
echo ""
echo "=========================================="
echo "请选择部署模式："
echo "=========================================="
echo "1. 批量模式 - 一次性生成2025年1-6月历史报告"
echo "2. 定时任务模式 - 设置定时任务，每月3号自动生成报告"
echo "3. 完整模式 - Web服务 + 定时任务 + 初始批量生成"
echo "4. 手动选择"
echo ""

read -p "请选择模式 (1-4): " choice

case $choice in
    1)
        echo "启动批量模式..."
        sudo docker-compose --profile batch up
        ;;
    2)
        echo "启动定时任务模式..."
        sudo docker-compose --profile cron up -d
        
        echo "✓ 定时任务服务启动成功"
        echo "定时任务已设置：每月3号凌晨2点自动生成上个月报告"
        echo ""
        echo "查看服务状态: sudo docker-compose --profile cron ps"
        echo "查看日志: sudo docker-compose --profile cron logs -f"
        echo "停止服务: sudo docker-compose --profile cron down"
        ;;
    3)
        echo "启动完整模式..."
        sudo docker-compose --profile daemon up -d
        
        echo "✓ 完整服务启动成功"
        echo "- 将先执行批量生成2025年1-6月报告"
        echo "- 然后设置定时任务每月3号自动生成"
        echo "- Web服务运行在端口1998"
        echo ""
        echo "查看服务状态: sudo docker-compose --profile daemon ps"
        echo "查看日志: sudo docker-compose --profile daemon logs -f"
        echo "停止服务: sudo docker-compose --profile daemon down"
        ;;
    4)
        echo ""
        echo "手动部署选项："
        echo ""
        echo "批量生成历史报告："
        echo "  sudo docker-compose --profile batch up"
        echo ""
        echo "启动定时任务服务："
        echo "  sudo docker-compose --profile cron up -d"
        echo ""
        echo "启动完整服务："
        echo "  sudo docker-compose --profile daemon up -d"
        echo ""
        echo "查看所有服务状态："
        echo "  sudo docker-compose ps"
        echo ""
        echo "查看日志："
        echo "  sudo docker-compose logs -f [service-name]"
        echo ""
        echo "停止服务："
        echo "  sudo docker-compose --profile [profile] down"
        ;;
    *)
        echo "无效选择，退出"
        exit 1
        ;;
esac

echo ""
echo "=========================================="
echo "部署完成！"
echo "=========================================="
echo "输出目录:"
echo "  报告文件: ./output/reports/"
echo "  图表文件: ./output/charts/"
echo "  日志文件: ./output/logs/"
echo ""
echo "常用命令:"
echo "  查看服务: sudo docker-compose ps"
echo "  查看日志: sudo docker-compose logs -f"
echo "  停止服务: sudo docker-compose down"
echo "  重启服务: sudo docker-compose restart"
echo "=========================================="
