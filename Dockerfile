# 使用Python 3.11官方镜像 - Microsoft镜像源选项
# 选择以下任一Microsoft镜像源（取消注释你想使用的）:

# 选项1: Microsoft DevContainers Bullseye (推荐，包含完整开发环境)
FROM mcr.microsoft.com/devcontainers/python:3.11-bullseye

# 其他Microsoft镜像选项:
# FROM mcr.microsoft.com/devcontainers/python:3.11-slim
# FROM mcr.microsoft.com/python:3.11-slim
# FROM mcr.microsoft.com/azure-functions/python:4-python3.11-slim

# 选项4: Microsoft .NET SDK with Python (如果需要.NET支持)
# FROM mcr.microsoft.com/dotnet/sdk:7.0

# 选项5: Microsoft Universal Base Image with Python
# FROM mcr.microsoft.com/ubi8/python-311

# 其他国外镜像源备选:
# FROM python:3.11-slim  # 官方Docker Hub
# FROM public.ecr.aws/lambda/python:3.11  # Amazon ECR

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖（包括cron和中文字体）- 适配Debian/Ubuntu
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    libmariadb-dev \
    pkg-config \
    fonts-dejavu-core \
    fontconfig \
    fonts-wqy-zenhei \
    fonts-wqy-microhei \
    fonts-noto-cjk \
    fonts-noto-cjk-extra \
    cron \
    && fc-cache -fv \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements.txt并安装Python依赖（使用国内镜像源）
COPY requirements.txt .
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/reports /app/charts /app/logs

# 设置权限
RUN chmod +x generate_monthly_reports.py setup_monthly_cron.py

# 创建启动脚本
COPY docker_entrypoint.sh /app/
RUN chmod +x /app/docker_entrypoint.sh

# 暴露端口（如果需要Web服务）
EXPOSE 1998

# 使用启动脚本作为入口点
ENTRYPOINT ["/app/docker_entrypoint.sh"]
