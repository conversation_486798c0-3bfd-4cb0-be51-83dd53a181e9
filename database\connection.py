"""
数据库连接管理模块
"""
import pymysql
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
from typing import Dict, Any, List, Optional
from loguru import logger
from config import settings


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._init_database()
    
    def _init_database(self):
        """初始化数据库连接"""
        try:
            # 对密码进行URL编码以处理特殊字符
            from urllib.parse import quote_plus
            encoded_password = quote_plus(settings.db_password)

            # 构建数据库连接URL，添加更多连接参数
            database_url = (
                f"mysql+pymysql://{settings.db_user}:{encoded_password}"
                f"@{settings.db_host}:{settings.db_port}/{settings.db_name}"
                f"?charset=utf8mb4&ssl_disabled=true&connect_timeout=30"
            )

            # 调试信息（隐藏密码）
            debug_url = (
                f"mysql+pymysql://{settings.db_user}:***"
                f"@{settings.db_host}:{settings.db_port}/{settings.db_name}"
                f"?charset=utf8mb4"
            )
            logger.debug(f"数据库连接URL: {debug_url}")

            # 创建数据库引擎，添加更多连接选项
            self.engine = create_engine(
                database_url,
                poolclass=QueuePool,
                pool_size=5,
                max_overflow=10,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=settings.app_debug,
                connect_args={
                    "ssl_disabled": True,
                    "connect_timeout": 30,
                    "read_timeout": 30,
                    "write_timeout": 30,
                    "charset": "utf8mb4"
                }
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            logger.info("数据库连接初始化成功")
            
        except Exception as e:
            logger.error(f"数据库连接初始化失败: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """获取数据库会话上下文管理器"""
        session = self.SessionLocal()
        try:
            yield session
            # 只有在没有异常时才提交
        except Exception as e:
            session.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """执行查询并返回结果"""
        try:
            with self.get_session() as session:
                result = session.execute(text(query), params or {})
                columns = result.keys()
                rows = result.fetchall()
                
                # 转换为字典列表
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            raise
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            # 使用更直接的连接测试
            with self.engine.connect() as connection:
                result = connection.execute(text("SELECT 1"))
                result.fetchone()
                logger.info("数据库连接测试成功")
                return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False

    def test_direct_connection(self) -> bool:
        """使用PyMySQL直接测试连接"""
        try:
            connection = pymysql.connect(
                host=settings.db_host,
                port=settings.db_port,
                user=settings.db_user,
                password=settings.db_password,
                database=settings.db_name,
                charset='utf8mb4',
                ssl_disabled=True,
                connect_timeout=30
            )

            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()

            connection.close()
            logger.info("直接数据库连接测试成功")
            return True

        except Exception as e:
            logger.error(f"直接数据库连接测试失败: {e}")
            return False


# 全局数据库管理器实例
db_manager = DatabaseManager()
